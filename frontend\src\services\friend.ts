/**
 * 好友管理相关 API 服务
 */

import { apiRequest } from '@/utils/request';
import type { Account, SetFriendRemarkRequest } from '@/types/api';

/**
 * 添加好友请求
 */
export interface AddFriendRequest {
  email: string;
}

/**
 * 从好友列表邀请成员请求
 */
export interface InviteFriendsRequest {
  friendIds: number[];
}

/**
 * 好友服务类
 *
 * 提供好友关系管理的所有API接口，包括：
 * - 添加/删除好友
 * - 获取好友列表和数量
 * - 搜索用户
 * - 检查好友关系
 * - 邀请好友加入团队
 */
export class FriendService {
  /**
   * 添加好友
   *
   * @param data 添加好友请求数据，包含好友邮箱
   * @returns Promise<void> 添加成功时resolve
   * @throws 当好友不存在、已是好友或其他业务错误时抛出异常
   */
  static async addFriend(data: AddFriendRequest): Promise<void> {
    await apiRequest.post<string>('/friends/add', data);
  }

  /**
   * 删除好友
   *
   * @param friendId 要删除的好友ID
   * @returns Promise<void> 删除成功时resolve
   * @throws 当好友关系不存在或其他业务错误时抛出异常
   */
  static async removeFriend(friendId: number): Promise<void> {
    await apiRequest.delete<string>(`/friends/remove?friendId=${friendId}`);
  }

  /**
   * 获取好友列表
   *
   * @returns Promise<Account[]> 当前用户的所有好友信息
   * @throws 当网络错误或服务器错误时抛出异常
   */
  static async getFriends(): Promise<Account[]> {
    const response = await apiRequest.get<Account[]>('/friends/list');
    return response.data;
  }

  /**
   * 检查好友关系
   */
  static async checkFriendship(userId: number): Promise<boolean> {
    const response = await apiRequest.get<boolean>(`/friends/check?userId=${userId}`);
    return response.data;
  }

  /**
   * 获取好友数量
   */
  static async getFriendCount(): Promise<number> {
    const response = await apiRequest.get<number>('/friends/count');
    return response.data;
  }

  /**
   * 搜索用户（用于添加好友）
   */
  static async searchUsers(email: string): Promise<Account[]> {
    const response = await apiRequest.get<Account[]>(`/users/search?email=${encodeURIComponent(email)}`);
    return response.data;
  }

  /**
   * 获取收到的好友请求列表
   */
  static async getReceivedFriendRequests(): Promise<any[]> {
    const response = await apiRequest.get<any[]>('/friends/requests/received');
    return response.data;
  }

  /**
   * 获取发送的好友请求列表
   */
  static async getSentFriendRequests(): Promise<any[]> {
    const response = await apiRequest.get<any[]>('/friends/requests/sent');
    return response.data;
  }

  /**
   * 接受好友请求
   */
  static async acceptFriendRequest(requestId: number): Promise<void> {
    await apiRequest.post<string>(`/friends/accept/${requestId}`);
  }

  /**
   * 拒绝好友请求
   */
  static async rejectFriendRequest(requestId: number): Promise<void> {
    await apiRequest.post<string>(`/friends/reject/${requestId}`);
  }

  /**
   * 取消好友请求
   */
  static async cancelFriendRequest(requestId: number): Promise<void> {
    await apiRequest.delete<string>(`/friends/request/${requestId}`);
  }

  /**
   * 发送好友请求
   */
  static async sendFriendRequest(data: AddFriendRequest): Promise<void> {
    await apiRequest.post<string>('/friends/request', data);
  }

  /**
   * 设置好友备注
   */
  static async setFriendRemark(data: SetFriendRemarkRequest): Promise<void> {
    await apiRequest.post<string>('/friends/remark', data);
  }

  /**
   * 获取好友备注
   */
  static async getFriendRemark(friendId: number): Promise<string> {
    const response = await apiRequest.get<string>(`/friends/remark?friendId=${friendId}`);
    return response.data;
  }

  /**
   * 从好友列表邀请成员到团队
   */
  static async inviteFriendsToTeam(data: InviteFriendsRequest): Promise<void> {
    await apiRequest.post<string>('/teams/current/members/invite-friends', data);
  }
}

// 导出默认实例
export default FriendService;
