/**
 * 好友请求管理组件
 */

import React, { useState, useEffect } from 'react';
import { 
  Card, 
  List, 
  Avatar, 
  Button, 
  Space, 
  Typography, 
  message,
  Empty,
  Tabs,
  Badge,
  Popconfirm
} from 'antd';
import { 
  UserOutlined, 
  CheckOutlined,
  CloseOutlined,
  InboxOutlined,
  SendOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { FriendService } from '@/services';
import type { Account } from '@/types/api';

const { Text, Title } = Typography;

interface FriendRequest {
  id: number;
  accountId: number;
  invitedBy: number;
  invitedAt: string;
  requestedAt: string;
  status: string;
  account?: Account;
  inviter?: Account;
}

interface FriendRequestsProps {
  onRequestHandled: () => void;
}

const FriendRequests: React.FC<FriendRequestsProps> = ({ onRequestHandled }) => {
  const [activeTab, setActiveTab] = useState('received');
  const [loading, setLoading] = useState(false);
  const [receivedRequests, setReceivedRequests] = useState<FriendRequest[]>([]);
  const [sentRequests, setSentRequests] = useState<FriendRequest[]>([]);
  const [processing, setProcessing] = useState<number | null>(null);

  useEffect(() => {
    fetchRequests();
  }, []);

  const fetchRequests = async () => {
    try {
      setLoading(true);
      const [received, sent] = await Promise.all([
        FriendService.getReceivedFriendRequests(),
        FriendService.getSentFriendRequests()
      ]);
      setReceivedRequests(received);
      setSentRequests(sent);
    } catch (error) {
      console.error('获取好友请求失败:', error);
      message.error('获取好友请求失败');
    } finally {
      setLoading(false);
    }
  };

  const handleAcceptRequest = async (requestId: number) => {
    try {
      setProcessing(requestId);
      await FriendService.acceptFriendRequest(requestId);
      message.success('已接受好友请求');
      fetchRequests();
      onRequestHandled();
    } catch (error) {
      console.error('接受好友请求失败:', error);
      message.error('接受好友请求失败');
    } finally {
      setProcessing(null);
    }
  };

  const handleRejectRequest = async (requestId: number) => {
    try {
      setProcessing(requestId);
      await FriendService.rejectFriendRequest(requestId);
      message.success('已拒绝好友请求');
      fetchRequests();
    } catch (error) {
      console.error('拒绝好友请求失败:', error);
      message.error('拒绝好友请求失败');
    } finally {
      setProcessing(null);
    }
  };

  const handleCancelRequest = async (requestId: number) => {
    try {
      setProcessing(requestId);
      await FriendService.cancelFriendRequest(requestId);
      message.success('已取消好友请求');
      fetchRequests();
    } catch (error) {
      console.error('取消好友请求失败:', error);
      message.error('取消好友请求失败');
    } finally {
      setProcessing(null);
    }
  };

  const renderReceivedRequests = () => (
    <List
      loading={loading}
      dataSource={receivedRequests}
      renderItem={(request) => (
        <List.Item
          actions={[
            <Button
              key="accept"
              type="primary"
              icon={<CheckOutlined />}
              loading={processing === request.id}
              onClick={() => handleAcceptRequest(request.id)}
              size="small"
            >
              接受
            </Button>,
            <Popconfirm
              key="reject"
              title="确认拒绝好友请求"
              description="确定要拒绝这个好友请求吗？"
              icon={<ExclamationCircleOutlined style={{ color: 'red' }} />}
              onConfirm={() => handleRejectRequest(request.id)}
              okText="确认拒绝"
              cancelText="取消"
              okType="danger"
            >
              <Button
                danger
                icon={<CloseOutlined />}
                loading={processing === request.id}
                size="small"
              >
                拒绝
              </Button>
            </Popconfirm>
          ]}
        >
          <List.Item.Meta
            avatar={
              <Avatar 
                size={48} 
                icon={<UserOutlined />}
                style={{ backgroundColor: '#1890ff' }}
              >
                {request.inviter?.name?.charAt(0).toUpperCase()}
              </Avatar>
            }
            title={
              <Space>
                <Text strong>{request.inviter?.name || '未知用户'}</Text>
              </Space>
            }
            description={
              <Space direction="vertical" size={4}>
                <Text type="secondary">{request.inviter?.email}</Text>
                <Text type="secondary" style={{ fontSize: 12 }}>
                  请求时间: {new Date(request.requestedAt).toLocaleString()}
                </Text>
              </Space>
            }
          />
        </List.Item>
      )}
      locale={{
        emptyText: (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description="暂无收到的好友请求"
          />
        )
      }}
    />
  );

  const renderSentRequests = () => (
    <List
      loading={loading}
      dataSource={sentRequests}
      renderItem={(request) => (
        <List.Item
          actions={[
            request.status === 'pending' ? (
              <Popconfirm
                key="cancel"
                title="确认取消好友请求"
                description="确定要取消这个好友请求吗？"
                icon={<ExclamationCircleOutlined style={{ color: 'red' }} />}
                onConfirm={() => handleCancelRequest(request.id)}
                okText="确认取消"
                cancelText="取消"
                okType="danger"
              >
                <Button
                  danger
                  icon={<CloseOutlined />}
                  loading={processing === request.id}
                  size="small"
                >
                  取消请求
                </Button>
              </Popconfirm>
            ) : (
              <Text type="secondary">
                {request.status === 'accepted' ? '已接受' : '已拒绝'}
              </Text>
            )
          ]}
        >
          <List.Item.Meta
            avatar={
              <Avatar 
                size={48} 
                icon={<UserOutlined />}
                style={{ backgroundColor: '#52c41a' }}
              >
                {request.account?.name?.charAt(0).toUpperCase()}
              </Avatar>
            }
            title={
              <Space>
                <Text strong>{request.account?.name || '未知用户'}</Text>
                <Badge 
                  status={
                    request.status === 'pending' ? 'processing' : 
                    request.status === 'accepted' ? 'success' : 'error'
                  }
                  text={
                    request.status === 'pending' ? '待处理' : 
                    request.status === 'accepted' ? '已接受' : '已拒绝'
                  }
                />
              </Space>
            }
            description={
              <Space direction="vertical" size={4}>
                <Text type="secondary">{request.account?.email}</Text>
                <Text type="secondary" style={{ fontSize: 12 }}>
                  发送时间: {new Date(request.invitedAt).toLocaleString()}
                </Text>
              </Space>
            }
          />
        </List.Item>
      )}
      locale={{
        emptyText: (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description="暂无发送的好友请求"
          />
        )
      }}
    />
  );

  const tabItems = [
    {
      key: 'received',
      label: (
        <Space>
          <InboxOutlined />
          收到的请求
          {receivedRequests.length > 0 && (
            <Badge count={receivedRequests.length} size="small" />
          )}
        </Space>
      ),
      children: renderReceivedRequests()
    },
    {
      key: 'sent',
      label: (
        <Space>
          <SendOutlined />
          发送的请求
          {sentRequests.length > 0 && (
            <Badge count={sentRequests.length} size="small" />
          )}
        </Space>
      ),
      children: renderSentRequests()
    }
  ];

  return (
    <Card
      title={
        <Space>
          <InboxOutlined />
          好友请求管理
        </Space>
      }
      extra={
        <Button onClick={fetchRequests} loading={loading}>
          刷新
        </Button>
      }
    >
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={tabItems}
      />
    </Card>
  );
};

export default FriendRequests;
