package com.teammanage.entity;

import java.time.LocalDateTime;
import java.util.Objects;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 账号邀请关系实体类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */

@TableName("account_relation")
public class AccountRelation extends BaseEntity {

    /**
     * 被邀请账号ID
     */
    private Long accountId;

    /**
     * 邀请账号ID
     */
    private Long invitedBy;

    /**
     * 邀请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime invitedAt;

    /**
     * 好友备注
     */
    private String remark;

    /**
     * 好友关系状态
     */
    private FriendStatus status;

    /**
     * 请求发送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime requestedAt;

    /**
     * 是否激活
     */
    private Boolean isActive;

    /**
     * 删除标记
     */
    @TableLogic
    private Boolean isDeleted;

    // Getter and Setter methods
    public Long getAccountId() { return accountId; }
    public void setAccountId(Long accountId) { this.accountId = accountId; }

    public Long getInvitedBy() { return invitedBy; }
    public void setInvitedBy(Long invitedBy) { this.invitedBy = invitedBy; }

    public LocalDateTime getInvitedAt() { return invitedAt; }
    public void setInvitedAt(LocalDateTime invitedAt) { this.invitedAt = invitedAt; }

    public String getRemark() { return remark; }
    public void setRemark(String remark) { this.remark = remark; }

    public FriendStatus getStatus() { return status; }
    public void setStatus(FriendStatus status) { this.status = status; }

    public LocalDateTime getRequestedAt() { return requestedAt; }
    public void setRequestedAt(LocalDateTime requestedAt) { this.requestedAt = requestedAt; }

    public Boolean getIsActive() { return isActive; }
    public void setIsActive(Boolean isActive) { this.isActive = isActive; }

    public Boolean getIsDeleted() { return isDeleted; }
    public void setIsDeleted(Boolean isDeleted) { this.isDeleted = isDeleted; }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        AccountRelation that = (AccountRelation) o;
        return Objects.equals(accountId, that.accountId) &&
               Objects.equals(invitedBy, that.invitedBy) &&
               Objects.equals(invitedAt, that.invitedAt) &&
               Objects.equals(remark, that.remark) &&
               Objects.equals(status, that.status) &&
               Objects.equals(requestedAt, that.requestedAt) &&
               Objects.equals(isActive, that.isActive) &&
               Objects.equals(isDeleted, that.isDeleted);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), accountId, invitedBy, invitedAt, remark, status, requestedAt, isActive, isDeleted);
    }

}
