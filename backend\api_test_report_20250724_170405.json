{"summary": {"test_run_timestamp": "2025-07-24T17:04:05.627296", "total_tests": 34, "passed": 24, "failed": 10, "skipped": 0, "success_rate": "70.6%", "base_url": "http://localhost:8080/api/v1"}, "categories": {"Authentication": {"total": 13, "passed": 9, "failed": 4, "success_rate": "69.2%"}, "Team Management": {"total": 2, "passed": 1, "failed": 1, "success_rate": "50.0%"}, "User Management": {"total": 5, "passed": 4, "failed": 1, "success_rate": "80.0%"}, "Friend Management": {"total": 9, "passed": 5, "failed": 4, "success_rate": "55.6%"}, "Subscriptions": {"total": 5, "passed": 5, "failed": 0, "success_rate": "100.0%"}}, "detailed_results": [{"timestamp": "2025-07-24T17:04:03.922317", "endpoint": "/auth/register", "method": "POST", "status": "FAIL", "status_code": 400, "message": "Register test user 1", "error": "", "response_data": {"code": 400, "message": "邮箱已被注册", "data": null, "timestamp": "2025-07-24 17:04:03"}}, {"timestamp": "2025-07-24T17:04:04.110175", "endpoint": "/auth/login", "method": "POST", "status": "PASS", "status_code": 200, "message": "Login existing user 1", "error": "", "response_data": {"code": 200, "message": "登录成功", "data": {"token": "eyJhbGciOiJIUzM4NCJ9.***************************************************************************************************************************************************************************************************************************************.2t19xcoeDx4n-eSu2RqUOoUlXINJJlgwzRB-ZSj9WbqxKTe03Y6ELJNRoVS-_DOZ", "expiresIn": 604799895, "user": {"id": 14, "email": "<EMAIL>", "name": "Updated Test User"}, "teams": [{"id": 13, "name": "Updated Test Team", "isCreator": true, "memberCount": 1, "lastAccessTime": "2025-07-24 16:36:33"}], "team": null, "teamSelectionSuccess": null}, "timestamp": "2025-07-24 17:04:04"}}, {"timestamp": "2025-07-24T17:04:04.128282", "endpoint": "/auth/register", "method": "POST", "status": "FAIL", "status_code": 400, "message": "Register test user 2", "error": "", "response_data": {"code": 400, "message": "邮箱已被注册", "data": null, "timestamp": "2025-07-24 17:04:04"}}, {"timestamp": "2025-07-24T17:04:04.306894", "endpoint": "/auth/login", "method": "POST", "status": "PASS", "status_code": 200, "message": "Login existing user 2", "error": "", "response_data": {"code": 200, "message": "登录成功", "data": {"token": "eyJhbGciOiJIUzM4NCJ9.*******************************************************************************************************************************************************************************************************************************._Y06hEOg1yOQXUCPqTQVJuM79cQmj1mSPr-AEumc_Ht8SoXi887qmEMOvbhWRFcm", "expiresIn": 604799698, "user": {"id": 15, "email": "<EMAIL>", "name": "Test User 2"}, "teams": [], "team": null, "teamSelectionSuccess": null}, "timestamp": "2025-07-24 17:04:04"}}, {"timestamp": "2025-07-24T17:04:04.322895", "endpoint": "/auth/register", "method": "POST", "status": "FAIL", "status_code": 400, "message": "Register test user 3", "error": "", "response_data": {"code": 400, "message": "邮箱已被注册", "data": null, "timestamp": "2025-07-24 17:04:04"}}, {"timestamp": "2025-07-24T17:04:04.503279", "endpoint": "/auth/login", "method": "POST", "status": "PASS", "status_code": 200, "message": "Login existing user 3", "error": "", "response_data": {"code": 200, "message": "登录成功", "data": {"token": "eyJhbGciOiJIUzM4NCJ9.*******************************************************************************************************************************************************************************************************************************.fy09JH5TPTE71o6a_umIh7_6gM7lVcw6ZWyVLl6xjqPxnOExBcn3neBh7PqJNRSx", "expiresIn": 604799500, "user": {"id": 16, "email": "<EMAIL>", "name": "Test User 3"}, "teams": [], "team": null, "teamSelectionSuccess": null}, "timestamp": "2025-07-24 17:04:04"}}, {"timestamp": "2025-07-24T17:04:04.514279", "endpoint": "/auth/register", "method": "POST", "status": "PASS", "status_code": 400, "message": "Register with invalid data", "error": "", "response_data": {"code": 400, "message": "参数验证失败: {password=密码长度至少8位, email=邮箱格式不正确}", "data": null, "timestamp": "2025-07-24 17:04:04"}}, {"timestamp": "2025-07-24T17:04:04.528505", "endpoint": "/auth/login", "method": "POST", "status": "PASS", "status_code": 400, "message": "Login with invalid credentials", "error": "", "response_data": {"code": 400, "message": "邮箱或密码错误", "data": null, "timestamp": "2025-07-24 17:04:04"}}, {"timestamp": "2025-07-24T17:04:04.542505", "endpoint": "/auth/validate", "method": "GET", "status": "PASS", "status_code": 200, "message": "Validate valid token", "error": "", "response_data": {"code": 200, "message": "Token验证结果", "data": true, "timestamp": "2025-07-24 17:04:04"}}, {"timestamp": "2025-07-24T17:04:04.555505", "endpoint": "/auth/validate", "method": "GET", "status": "PASS", "status_code": 401, "message": "Validate invalid token", "error": "", "response_data": {"code": 401, "message": "未认证，请先登录", "data": null, "timestamp": "2025-07-24 17:04:04"}}, {"timestamp": "2025-07-24T17:04:04.593505", "endpoint": "/auth/refresh-token", "method": "POST", "status": "PASS", "status_code": 200, "message": "Refresh valid token", "error": "", "response_data": {"code": 200, "message": "Token刷新成功", "data": {"token": "eyJhbGciOiJIUzM4NCJ9.***************************************************************************************************************************************************************************************************************************************.3nhL1uQR9mrUefg9km6_bsMJXf5RlCBOXEai8nbFis4vPaFLFVxZMsZW1Ne30HRU", "expiresIn": 604799413, "user": null, "teams": null, "team": null, "teamSelectionSuccess": null}, "timestamp": "2025-07-24 17:04:04"}}, {"timestamp": "2025-07-24T17:04:04.782863", "endpoint": "/auth/login", "method": "POST", "status": "PASS", "status_code": 200, "message": "Login user 1 for backup token", "error": "", "response_data": {"code": 200, "message": "登录成功", "data": {"token": "eyJhbGciOiJIUzM4NCJ9.***************************************************************************************************************************************************************************************************************************************.mZiQWOQ3tWQd0WalN-64-4dpUrtQCveu4VSTUhxAkk-41vFFGwjAADee6slxyKxB", "expiresIn": 604799222, "user": {"id": 14, "email": "<EMAIL>", "name": "Updated Test User"}, "teams": [{"id": 13, "name": "Updated Test Team", "isCreator": true, "memberCount": 1, "lastAccessTime": "2025-07-24 16:36:33"}], "team": null, "teamSelectionSuccess": null}, "timestamp": "2025-07-24 17:04:04"}}, {"timestamp": "2025-07-24T17:04:04.791867", "endpoint": "/auth/logout", "method": "POST", "status": "FAIL", "status_code": 401, "message": "Logout with valid token", "error": "", "response_data": {"code": 401, "message": "未认证，请先登录", "data": null, "timestamp": "2025-07-24 17:04:04"}}, {"timestamp": "2025-07-24T17:04:04.804862", "endpoint": "/plans", "method": "GET", "status": "PASS", "status_code": 200, "message": "Get subscription plans", "error": "", "response_data": {"code": 200, "message": "success", "data": [{"id": 1, "name": "免费版", "description": "适合个人用户，可创建1个团队", "maxSize": 1, "price": 0.0, "isActive": true, "createdAt": "2025-07-23 09:40:17", "updatedAt": "2025-07-24 14:57:08"}, {"id": 2, "name": "标准版", "description": "适合小团队，可创建5个团队", "maxSize": 5, "price": 99.0, "isActive": true, "createdAt": "2025-07-23 09:40:17", "updatedAt": "2025-07-24 14:57:08"}, {"id": 3, "name": "专业版", "description": "适合中型企业，可创建20个团队", "maxSize": 20, "price": 299.0, "isActive": true, "createdAt": "2025-07-23 09:40:17", "updatedAt": "2025-07-24 14:57:08"}, {"id": 4, "name": "企业版", "description": "适合大型企业，可创建无限团队", "maxSize": 999999, "price": 999.0, "isActive": true, "createdAt": "2025-07-23 09:40:17", "updatedAt": "2025-07-24 14:57:08"}], "timestamp": "2025-07-24 17:04:04"}}, {"timestamp": "2025-07-24T17:04:04.831865", "endpoint": "/subscriptions", "method": "GET", "status": "PASS", "status_code": 200, "message": "Get user subscriptions", "error": "", "response_data": {"code": 200, "message": "success", "data": [{"id": 10, "accountId": 14, "subscriptionPlanId": 1, "planName": "免费版", "planDescription": "适合个人用户，可创建1个团队", "maxSize": 1, "price": 0.0, "startDate": "2025-07-24", "endDate": null, "status": "CANCELED", "createdAt": "2025-07-24 17:00:10", "updatedAt": "2025-07-24 17:00:10"}, {"id": 9, "accountId": 14, "subscriptionPlanId": 1, "planName": "免费版", "planDescription": "适合个人用户，可创建1个团队", "maxSize": 1, "price": 0.0, "startDate": "2025-07-24", "endDate": null, "status": "CANCELED", "createdAt": "2025-07-24 16:45:58", "updatedAt": "2025-07-24 16:45:58"}, {"id": 8, "accountId": 14, "subscriptionPlanId": 1, "planName": "免费版", "planDescription": "适合个人用户，可创建1个团队", "maxSize": 1, "price": 0.0, "startDate": "2025-07-24", "endDate": null, "status": "CANCELED", "createdAt": "2025-07-24 16:36:33", "updatedAt": "2025-07-24 16:36:33"}], "timestamp": "2025-07-24 17:04:04"}}, {"timestamp": "2025-07-24T17:04:04.845144", "endpoint": "/subscriptions/current", "method": "GET", "status": "PASS", "status_code": 200, "message": "Get current subscription", "error": "", "response_data": {"code": 200, "message": "success", "data": null, "timestamp": "2025-07-24 17:04:04"}}, {"timestamp": "2025-07-24T17:04:04.938146", "endpoint": "/subscriptions", "method": "POST", "status": "PASS", "status_code": 200, "message": "Create subscription", "error": "", "response_data": {"code": 200, "message": "订阅创建成功", "data": {"id": 11, "accountId": 14, "subscriptionPlanId": 1, "planName": "免费版", "planDescription": "适合个人用户，可创建1个团队", "maxSize": 1, "price": 0.0, "startDate": "2025-07-24", "endDate": null, "status": "ACTIVE", "createdAt": "2025-07-24 17:04:04", "updatedAt": "2025-07-24 17:04:04"}, "timestamp": "2025-07-24 17:04:04"}}, {"timestamp": "2025-07-24T17:04:04.988650", "endpoint": "/subscriptions/11", "method": "DELETE", "status": "PASS", "status_code": 200, "message": "Cancel subscription", "error": "", "response_data": {"code": 200, "message": "订阅取消成功", "data": null, "timestamp": "2025-07-24 17:04:04"}}, {"timestamp": "2025-07-24T17:04:05.018653", "endpoint": "/teams", "method": "GET", "status": "PASS", "status_code": 200, "message": "Get user teams", "error": "", "response_data": {"code": 200, "message": "success", "data": [{"id": 13, "name": "Updated Test Team", "description": "Updated description", "createdBy": 14, "memberCount": 1, "isCreator": true, "lastAccessTime": "2025-07-24 16:36:33", "createdAt": "2025-07-24 16:36:33", "updatedAt": "2025-07-24 16:36:33"}], "timestamp": "2025-07-24 17:04:05"}}, {"timestamp": "2025-07-24T17:04:05.037965", "endpoint": "/teams", "method": "POST", "status": "FAIL", "status_code": 400, "message": "Create new team", "error": "", "response_data": {"code": 400, "message": "已达到订阅套餐的团队创建数量限制，当前限制：1个团队", "data": null, "timestamp": "2025-07-24 17:04:05"}}, {"timestamp": "2025-07-24T17:04:05.057962", "endpoint": "/users/profile", "method": "GET", "status": "PASS", "status_code": 200, "message": "Get user profile", "error": "", "response_data": {"code": 200, "message": "success", "data": {"id": 14, "email": "<EMAIL>", "name": "Updated Test User", "defaultSubscriptionPlanId": 1, "createdAt": "2025-07-24 16:32:03", "updatedAt": "2025-07-24 16:32:03"}, "timestamp": "2025-07-24 17:04:05"}}, {"timestamp": "2025-07-24T17:04:05.078990", "endpoint": "/users/profile", "method": "PUT", "status": "PASS", "status_code": 200, "message": "Update user profile", "error": "", "response_data": {"code": 200, "message": "用户资料更新成功", "data": {"id": 14, "email": "<EMAIL>", "name": "Updated Test User", "defaultSubscriptionPlanId": 1, "createdAt": "2025-07-24 16:32:03", "updatedAt": "2025-07-24 16:32:03"}, "timestamp": "2025-07-24 17:04:05"}}, {"timestamp": "2025-07-24T17:04:05.238557", "endpoint": "/users/validate-password", "method": "POST", "status": "PASS", "status_code": 200, "message": "Validate user password", "error": "", "response_data": {"code": 200, "message": "success", "data": true, "timestamp": "2025-07-24 17:04:05"}}, {"timestamp": "2025-07-24T17:04:05.397845", "endpoint": "/users/validate-password", "method": "POST", "status": "FAIL", "status_code": 200, "message": "Validate wrong password (should fail)", "error": "", "response_data": {"code": 200, "message": "success", "data": false, "timestamp": "2025-07-24 17:04:05"}}, {"timestamp": "2025-07-24T17:04:05.416572", "endpoint": "/friends/request", "method": "POST", "status": "FAIL", "status_code": 400, "message": "Send friend request", "error": "", "response_data": {"code": 400, "message": "已经是好友关系或存在待处理的请求", "data": null, "timestamp": "2025-07-24 17:04:05"}}, {"timestamp": "2025-07-24T17:04:05.439631", "endpoint": "/friends/request", "method": "POST", "status": "PASS", "status_code": 400, "message": "Send friend request (already exists - expected)", "error": "", "response_data": {"code": 400, "message": "已经是好友关系或存在待处理的请求", "data": null, "timestamp": "2025-07-24 17:04:05"}}, {"timestamp": "2025-07-24T17:04:05.485445", "endpoint": "/friends/requests/sent", "method": "GET", "status": "FAIL", "status_code": 500, "message": "Get sent friend requests (known DB issue)", "error": "", "response_data": {"code": 500, "message": "系统内部错误，请联系管理员", "data": null, "timestamp": "2025-07-24 17:04:05"}}, {"timestamp": "2025-07-24T17:04:05.528450", "endpoint": "/friends/requests/received", "method": "GET", "status": "FAIL", "status_code": 500, "message": "Get received friend requests (known DB issue)", "error": "", "response_data": {"code": 500, "message": "系统内部错误，请联系管理员", "data": null, "timestamp": "2025-07-24 17:04:05"}}, {"timestamp": "2025-07-24T17:04:05.546447", "endpoint": "/friends/list", "method": "GET", "status": "PASS", "status_code": 200, "message": "Get friends list", "error": "", "response_data": {"code": 200, "message": "success", "data": [], "timestamp": "2025-07-24 17:04:05"}}, {"timestamp": "2025-07-24T17:04:05.564522", "endpoint": "/friends/count", "method": "GET", "status": "PASS", "status_code": 200, "message": "Get friend count", "error": "", "response_data": {"code": 200, "message": "success", "data": 0, "timestamp": "2025-07-24 17:04:05"}}, {"timestamp": "2025-07-24T17:04:05.577203", "endpoint": "/users/profile", "method": "GET", "status": "PASS", "status_code": 200, "message": "Get user 2 profile for friendship check", "error": "", "response_data": {"code": 200, "message": "success", "data": {"id": 15, "email": "<EMAIL>", "name": "Test User 2", "defaultSubscriptionPlanId": 1, "createdAt": "2025-07-24 16:32:04", "updatedAt": "2025-07-24 16:32:04"}, "timestamp": "2025-07-24 17:04:05"}}, {"timestamp": "2025-07-24T17:04:05.594196", "endpoint": "/friends/check", "method": "GET", "status": "PASS", "status_code": 200, "message": "Check friendship status", "error": "", "response_data": {"code": 200, "message": "success", "data": true, "timestamp": "2025-07-24 17:04:05"}}, {"timestamp": "2025-07-24T17:04:05.611155", "endpoint": "/friends/list", "method": "GET", "status": "PASS", "status_code": 200, "message": "Get friends list for remark test", "error": "", "response_data": {"code": 200, "message": "success", "data": [], "timestamp": "2025-07-24 17:04:05"}}, {"timestamp": "2025-07-24T17:04:05.626303", "endpoint": "/friends/request", "method": "POST", "status": "FAIL", "status_code": 404, "message": "Send friend request to non-existent user", "error": "", "response_data": {"code": 404, "message": "用户不存在", "data": null, "timestamp": "2025-07-24 17:04:05"}}]}