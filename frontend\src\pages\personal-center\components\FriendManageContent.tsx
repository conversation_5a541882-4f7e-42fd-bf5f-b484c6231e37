/**
 * 个人中心 - 好友管理内容组件
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  List,
  Avatar,
  Button,
  Space,
  Typography,
  Input,
  message,
  Empty,
  Modal,
  Popconfirm,
  Form,
  Tooltip,
  Row,
  Col,
  Divider,
  Pagination,
  Spin,
  Tabs,
  Badge
} from 'antd';
import {
  UserOutlined,
  DeleteOutlined,
  SearchOutlined,
  ExclamationCircleOutlined,
  EditOutlined,
  UserAddOutlined,
  ReloadOutlined,
  UnorderedListOutlined,
  InboxOutlined
} from '@ant-design/icons';
import { FriendService, UserService } from '@/services';
import type { Account } from '@/types/api';

const { Text, Title } = Typography;
const { Search } = Input;

const FriendManageContent: React.FC = () => {
  // 标签页状态
  const [activeTab, setActiveTab] = useState('list');
  
  // 好友列表状态
  const [friends, setFriends] = useState<Account[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(8);

  // 操作状态
  const [removing, setRemoving] = useState<number | null>(null);
  const [remarkModalVisible, setRemarkModalVisible] = useState(false);
  const [addFriendModalVisible, setAddFriendModalVisible] = useState(false);
  const [currentFriend, setCurrentFriend] = useState<Account | null>(null);

  // 表单
  const [remarkForm] = Form.useForm();
  const [addFriendForm] = Form.useForm();

  // 添加好友相关状态
  const [searchUsers, setSearchUsers] = useState<Account[]>([]);
  const [searchingUsers, setSearchingUsers] = useState(false);
  const [addingFriend, setAddingFriend] = useState<number | null>(null);

  // 获取好友列表
  const fetchFriends = async () => {
    try {
      setLoading(true);
      const friendList = await FriendService.getFriends();
      setFriends(friendList);
    } catch (error) {
      console.error('获取好友列表失败:', error);
      message.error('获取好友列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    fetchFriends();
  }, []);

  // 过滤好友列表
  const filteredFriends = friends.filter(friend =>
    friend.name?.toLowerCase().includes(searchKeyword.toLowerCase()) ||
    friend.email?.toLowerCase().includes(searchKeyword.toLowerCase())
  );

  // 分页处理
  const paginatedFriends = filteredFriends.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  );

  // 删除好友
  const handleRemoveFriend = async (friend: Account) => {
    try {
      setRemoving(friend.id);
      await FriendService.removeFriend(friend.id);
      message.success(`已删除好友 "${friend.name}"`);
      fetchFriends();
    } catch (error) {
      console.error('删除好友失败:', error);
      message.error('删除好友失败');
    } finally {
      setRemoving(null);
    }
  };

  // 编辑备注
  const handleEditRemark = async (friend: Account) => {
    setCurrentFriend(friend);
    setRemarkModalVisible(true);
    
    try {
      // 获取当前备注
      const remark = await FriendService.getFriendRemark(friend.id);
      remarkForm.setFieldsValue({ remark });
    } catch (error) {
      console.error('获取好友备注失败:', error);
      remarkForm.setFieldsValue({ remark: '' });
    }
  };

  // 保存备注
  const handleSaveRemark = async (values: { remark: string }) => {
    if (!currentFriend) return;

    try {
      await FriendService.setFriendRemark({
        friendId: currentFriend.id,
        remark: values.remark
      });
      message.success('备注保存成功');
      setRemarkModalVisible(false);
      setCurrentFriend(null);
      remarkForm.resetFields();
      fetchFriends();
    } catch (error) {
      console.error('保存备注失败:', error);
      message.error('保存备注失败');
    }
  };

  // 搜索用户
  const handleSearchUsers = async (email: string) => {
    if (!email.trim()) {
      setSearchUsers([]);
      return;
    }

    try {
      setSearchingUsers(true);
      const users = await UserService.searchUsersByEmail(email);
      // 过滤掉已经是好友的用户
      const friendIds = friends.map(f => f.id);
      const availableUsers = users.filter(user => !friendIds.includes(user.id));
      setSearchUsers(availableUsers);
    } catch (error) {
      console.error('搜索用户失败:', error);
      message.error('搜索用户失败');
    } finally {
      setSearchingUsers(false);
    }
  };

  // 添加好友
  const handleAddFriend = async (user: Account) => {
    try {
      setAddingFriend(user.id);
      await FriendService.sendFriendRequest(user.id);
      message.success(`已向 "${user.name}" 发送好友请求`);
      setSearchUsers(searchUsers.filter(u => u.id !== user.id));
    } catch (error) {
      console.error('发送好友请求失败:', error);
      message.error('发送好友请求失败');
    } finally {
      setAddingFriend(null);
    }
  };

  // 打开添加好友模态框
  const handleOpenAddFriend = () => {
    setAddFriendModalVisible(true);
    setSearchUsers([]);
    addFriendForm.resetFields();
  };

  // 好友列表组件
  const renderFriendList = () => (
    <div>
      {/* 头部操作区 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={8}>
          <Search
            placeholder="搜索好友姓名或邮箱"
            allowClear
            value={searchKeyword}
            onChange={(e) => setSearchKeyword(e.target.value)}
            prefix={<SearchOutlined />}
          />
        </Col>
        <Col xs={24} sm={12} md={16} style={{ textAlign: 'right' }}>
          <Space>
            <Button
              type="primary"
              icon={<UserAddOutlined />}
              onClick={handleOpenAddFriend}
            >
              添加好友
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={fetchFriends}
              loading={loading}
            >
              刷新
            </Button>
          </Space>
        </Col>
      </Row>

      {/* 好友统计 */}
      <Card size="small" style={{ marginBottom: 16 }}>
        <Space split={<Divider type="vertical" />}>
          <Text>
            <strong>好友总数：</strong>
            <Text type="success">{friends.length}</Text>
          </Text>
          <Text>
            <strong>当前显示：</strong>
            <Text type="primary">{filteredFriends.length}</Text>
          </Text>
        </Space>
      </Card>

      {/* 好友列表 */}
      <Card>
        <Spin spinning={loading}>
          <List
            dataSource={paginatedFriends}
            renderItem={(friend) => (
              <List.Item
                actions={[
                  <Tooltip key="remark" title="编辑备注">
                    <Button
                      type="text"
                      icon={<EditOutlined />}
                      onClick={() => handleEditRemark(friend)}
                      size="small"
                    >
                      备注
                    </Button>
                  </Tooltip>,
                  <Popconfirm
                    key="delete"
                    title="确认删除好友"
                    description={`确定要删除好友 "${friend.name}" 吗？`}
                    icon={<ExclamationCircleOutlined style={{ color: 'red' }} />}
                    onConfirm={() => handleRemoveFriend(friend)}
                    okText="确认删除"
                    cancelText="取消"
                    okType="danger"
                  >
                    <Button
                      type="text"
                      danger
                      icon={<DeleteOutlined />}
                      loading={removing === friend.id}
                      size="small"
                    >
                      删除
                    </Button>
                  </Popconfirm>
                ]}
              >
                <List.Item.Meta
                  avatar={
                    <Avatar 
                      size={48} 
                      icon={<UserOutlined />}
                      style={{ backgroundColor: '#1890ff' }}
                    >
                      {friend.name?.charAt(0).toUpperCase()}
                    </Avatar>
                  }
                  title={
                    <Space>
                      <Text strong>{friend.name}</Text>
                      {friend.remark && (
                        <Text type="secondary" style={{ fontSize: 12 }}>
                          ({friend.remark})
                        </Text>
                      )}
                    </Space>
                  }
                  description={
                    <Space direction="vertical" size={4}>
                      <Text type="secondary">{friend.email}</Text>
                      <Text type="secondary" style={{ fontSize: 12 }}>
                        添加时间: {friend.createdAt ? new Date(friend.createdAt).toLocaleDateString() : '未知'}
                      </Text>
                    </Space>
                  }
                />
              </List.Item>
            )}
            locale={{
              emptyText: (
                <Empty
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                  description={
                    searchKeyword 
                      ? `没有找到包含 "${searchKeyword}" 的好友`
                      : "暂无好友，点击上方按钮添加好友"
                  }
                />
              )
            }}
          />

          {/* 分页 */}
          {filteredFriends.length > pageSize && (
            <div style={{ textAlign: 'center', marginTop: 16 }}>
              <Pagination
                current={currentPage}
                pageSize={pageSize}
                total={filteredFriends.length}
                onChange={setCurrentPage}
                showSizeChanger={false}
                showQuickJumper
                showTotal={(total, range) => 
                  `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                }
              />
            </div>
          )}
        </Spin>
      </Card>
    </div>
  );

  // 添加好友组件
  const renderAddFriend = () => (
    <Card>
      <Title level={4}>添加好友</Title>
      <Form
        form={addFriendForm}
        layout="vertical"
        onFinish={(values) => handleSearchUsers(values.email)}
      >
        <Form.Item
          label="搜索用户"
          name="email"
          rules={[
            { required: true, message: '请输入邮箱' },
            { type: 'email', message: '请输入有效的邮箱地址' }
          ]}
        >
          <Search
            placeholder="请输入用户邮箱进行搜索"
            enterButton="搜索"
            loading={searchingUsers}
            onSearch={handleSearchUsers}
          />
        </Form.Item>
      </Form>

      {/* 搜索结果 */}
      {searchUsers.length > 0 && (
        <div style={{ marginTop: 24 }}>
          <Title level={5}>搜索结果</Title>
          <List
            dataSource={searchUsers}
            renderItem={(user) => (
              <List.Item
                actions={[
                  <Button
                    key="add"
                    type="primary"
                    icon={<UserAddOutlined />}
                    loading={addingFriend === user.id}
                    onClick={() => handleAddFriend(user)}
                    size="small"
                  >
                    添加好友
                  </Button>
                ]}
              >
                <List.Item.Meta
                  avatar={
                    <Avatar
                      size={40}
                      icon={<UserOutlined />}
                      style={{ backgroundColor: '#52c41a' }}
                    >
                      {user.name?.charAt(0).toUpperCase()}
                    </Avatar>
                  }
                  title={user.name}
                  description={user.email}
                />
              </List.Item>
            )}
          />
        </div>
      )}

      {searchUsers.length === 0 && addFriendForm.getFieldValue('email') && !searchingUsers && (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="未找到匹配的用户"
          style={{ marginTop: 24 }}
        />
      )}
    </Card>
  );

  const tabItems = [
    {
      key: 'list',
      label: (
        <Space>
          <UnorderedListOutlined />
          我的好友
          <Badge count={friends.length} showZero />
        </Space>
      ),
      children: renderFriendList()
    },
    {
      key: 'add',
      label: (
        <Space>
          <UserAddOutlined />
          添加好友
        </Space>
      ),
      children: renderAddFriend()
    }
  ];

  return (
    <div>
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={tabItems}
        size="large"
      />

      {/* 备注编辑模态框 */}
      <Modal
        title="编辑好友备注"
        open={remarkModalVisible}
        onCancel={() => {
          setRemarkModalVisible(false);
          setCurrentFriend(null);
          remarkForm.resetFields();
        }}
        footer={null}
        width={400}
      >
        <Form
          form={remarkForm}
          layout="vertical"
          onFinish={handleSaveRemark}
        >
          <Form.Item
            label="好友备注"
            name="remark"
            rules={[
              { max: 50, message: '备注长度不能超过50个字符' }
            ]}
          >
            <Input.TextArea
              placeholder="为好友添加备注..."
              rows={3}
              maxLength={50}
              showCount
            />
          </Form.Item>
          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setRemarkModalVisible(false);
                setCurrentFriend(null);
                remarkForm.resetFields();
              }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                保存
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default FriendManageContent;
