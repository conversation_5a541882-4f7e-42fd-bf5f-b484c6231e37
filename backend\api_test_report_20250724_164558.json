{"summary": {"test_run_timestamp": "2025-07-24T16:45:58.846967", "total_tests": 33, "passed": 24, "failed": 9, "skipped": 0, "success_rate": "72.7%", "base_url": "http://localhost:8080/api/v1"}, "categories": {"Authentication": {"total": 13, "passed": 9, "failed": 4, "success_rate": "69.2%"}, "Team Management": {"total": 2, "passed": 1, "failed": 1, "success_rate": "50.0%"}, "User Management": {"total": 5, "passed": 4, "failed": 1, "success_rate": "80.0%"}, "Friend Management": {"total": 8, "passed": 5, "failed": 3, "success_rate": "62.5%"}, "Subscriptions": {"total": 5, "passed": 5, "failed": 0, "success_rate": "100.0%"}}, "detailed_results": [{"timestamp": "2025-07-24T16:45:57.220834", "endpoint": "/auth/register", "method": "POST", "status": "FAIL", "status_code": 400, "message": "Register test user 1", "error": "", "response_data": {"code": 400, "message": "邮箱已被注册", "data": null, "timestamp": "2025-07-24 16:45:57"}}, {"timestamp": "2025-07-24T16:45:57.395431", "endpoint": "/auth/login", "method": "POST", "status": "PASS", "status_code": 200, "message": "Login existing user 1", "error": "", "response_data": {"code": 200, "message": "登录成功", "data": {"token": "eyJhbGciOiJIUzM4NCJ9.*******************************************************************************************************************************************************************************************************************************.0UNKuxQ2jda8p56W83QW4vP_DYYvs-dMHSziKiZSMBrzSJdGfG0yFrCYZOy5uA57", "expiresIn": 604799609, "user": {"id": 14, "email": "<EMAIL>", "name": "Test User 1"}, "teams": [{"id": 13, "name": "Updated Test Team", "isCreator": true, "memberCount": 1, "lastAccessTime": "2025-07-24 16:36:33"}], "team": null, "teamSelectionSuccess": null}, "timestamp": "2025-07-24 16:45:57"}}, {"timestamp": "2025-07-24T16:45:57.410451", "endpoint": "/auth/register", "method": "POST", "status": "FAIL", "status_code": 400, "message": "Register test user 2", "error": "", "response_data": {"code": 400, "message": "邮箱已被注册", "data": null, "timestamp": "2025-07-24 16:45:57"}}, {"timestamp": "2025-07-24T16:45:57.585195", "endpoint": "/auth/login", "method": "POST", "status": "PASS", "status_code": 200, "message": "Login existing user 2", "error": "", "response_data": {"code": 200, "message": "登录成功", "data": {"token": "eyJhbGciOiJIUzM4NCJ9.*******************************************************************************************************************************************************************************************************************************.FYBM032kqQqew8BnEJ61y7os8siZc_Nw8DVIuQUftmGNT_VxHFmUxwymBdedgZyq", "expiresIn": 604799419, "user": {"id": 15, "email": "<EMAIL>", "name": "Test User 2"}, "teams": [], "team": null, "teamSelectionSuccess": null}, "timestamp": "2025-07-24 16:45:57"}}, {"timestamp": "2025-07-24T16:45:57.603233", "endpoint": "/auth/register", "method": "POST", "status": "FAIL", "status_code": 400, "message": "Register test user 3", "error": "", "response_data": {"code": 400, "message": "邮箱已被注册", "data": null, "timestamp": "2025-07-24 16:45:57"}}, {"timestamp": "2025-07-24T16:45:57.774386", "endpoint": "/auth/login", "method": "POST", "status": "PASS", "status_code": 200, "message": "Login existing user 3", "error": "", "response_data": {"code": 200, "message": "登录成功", "data": {"token": "eyJhbGciOiJIUzM4NCJ9.*******************************************************************************************************************************************************************************************************************************.EUfLPndoiknBitmUlIEHUYg134eMnhalIxiwBq1gfHCGYi1VE2-AKCrTxhD-zOGW", "expiresIn": 604799230, "user": {"id": 16, "email": "<EMAIL>", "name": "Test User 3"}, "teams": [], "team": null, "teamSelectionSuccess": null}, "timestamp": "2025-07-24 16:45:57"}}, {"timestamp": "2025-07-24T16:45:57.788070", "endpoint": "/auth/register", "method": "POST", "status": "PASS", "status_code": 400, "message": "Register with invalid data", "error": "", "response_data": {"code": 400, "message": "参数验证失败: {password=密码长度至少8位, email=邮箱格式不正确}", "data": null, "timestamp": "2025-07-24 16:45:57"}}, {"timestamp": "2025-07-24T16:45:57.804830", "endpoint": "/auth/login", "method": "POST", "status": "PASS", "status_code": 400, "message": "Login with invalid credentials", "error": "", "response_data": {"code": 400, "message": "邮箱或密码错误", "data": null, "timestamp": "2025-07-24 16:45:57"}}, {"timestamp": "2025-07-24T16:45:57.817848", "endpoint": "/auth/validate", "method": "GET", "status": "PASS", "status_code": 200, "message": "Validate valid token", "error": "", "response_data": {"code": 200, "message": "Token验证结果", "data": true, "timestamp": "2025-07-24 16:45:57"}}, {"timestamp": "2025-07-24T16:45:57.825377", "endpoint": "/auth/validate", "method": "GET", "status": "PASS", "status_code": 401, "message": "Validate invalid token", "error": "", "response_data": {"code": 401, "message": "未认证，请先登录", "data": null, "timestamp": "2025-07-24 16:45:57"}}, {"timestamp": "2025-07-24T16:45:57.864494", "endpoint": "/auth/refresh-token", "method": "POST", "status": "PASS", "status_code": 200, "message": "Refresh valid token", "error": "", "response_data": {"code": 200, "message": "Token刷新成功", "data": {"token": "eyJhbGciOiJIUzM4NCJ9.*******************************************************************************************************************************************************************************************************************************.CCr3f-_KXaukxGqf_jlkD4imhGXqzwCxCachwuaFhPm1hPjKO6C1wLvVLuTyqd7B", "expiresIn": 604799140, "user": null, "teams": null, "team": null, "teamSelectionSuccess": null}, "timestamp": "2025-07-24 16:45:57"}}, {"timestamp": "2025-07-24T16:45:58.046498", "endpoint": "/auth/login", "method": "POST", "status": "PASS", "status_code": 200, "message": "Login user 1 for backup token", "error": "", "response_data": {"code": 200, "message": "登录成功", "data": {"token": "eyJhbGciOiJIUzM4NCJ9.*******************************************************************************************************************************************************************************************************************************.RBrceuEF6Bh60F2APyl1SIGYa4Qj494yBbXjCNhYlKN5gBHCAYUTwsug2SZd5fUk", "expiresIn": 604799961, "user": {"id": 14, "email": "<EMAIL>", "name": "Test User 1"}, "teams": [{"id": 13, "name": "Updated Test Team", "isCreator": true, "memberCount": 1, "lastAccessTime": "2025-07-24 16:36:33"}], "team": null, "teamSelectionSuccess": null}, "timestamp": "2025-07-24 16:45:58"}}, {"timestamp": "2025-07-24T16:45:58.054498", "endpoint": "/auth/logout", "method": "POST", "status": "FAIL", "status_code": 401, "message": "Logout with valid token", "error": "", "response_data": {"code": 401, "message": "未认证，请先登录", "data": null, "timestamp": "2025-07-24 16:45:58"}}, {"timestamp": "2025-07-24T16:45:58.069518", "endpoint": "/plans", "method": "GET", "status": "PASS", "status_code": 200, "message": "Get subscription plans", "error": "", "response_data": {"code": 200, "message": "success", "data": [{"id": 1, "name": "免费版", "description": "适合个人用户，可创建1个团队", "maxSize": 1, "price": 0.0, "isActive": true, "createdAt": "2025-07-23 09:40:17", "updatedAt": "2025-07-24 14:57:08"}, {"id": 2, "name": "标准版", "description": "适合小团队，可创建5个团队", "maxSize": 5, "price": 99.0, "isActive": true, "createdAt": "2025-07-23 09:40:17", "updatedAt": "2025-07-24 14:57:08"}, {"id": 3, "name": "专业版", "description": "适合中型企业，可创建20个团队", "maxSize": 20, "price": 299.0, "isActive": true, "createdAt": "2025-07-23 09:40:17", "updatedAt": "2025-07-24 14:57:08"}, {"id": 4, "name": "企业版", "description": "适合大型企业，可创建无限团队", "maxSize": 999999, "price": 999.0, "isActive": true, "createdAt": "2025-07-23 09:40:17", "updatedAt": "2025-07-24 14:57:08"}], "timestamp": "2025-07-24 16:45:58"}}, {"timestamp": "2025-07-24T16:45:58.088534", "endpoint": "/subscriptions", "method": "GET", "status": "PASS", "status_code": 200, "message": "Get user subscriptions", "error": "", "response_data": {"code": 200, "message": "success", "data": [{"id": 8, "accountId": 14, "subscriptionPlanId": 1, "planName": "免费版", "planDescription": "适合个人用户，可创建1个团队", "maxSize": 1, "price": 0.0, "startDate": "2025-07-24", "endDate": null, "status": "CANCELED", "createdAt": "2025-07-24 16:36:33", "updatedAt": "2025-07-24 16:36:33"}], "timestamp": "2025-07-24 16:45:58"}}, {"timestamp": "2025-07-24T16:45:58.104570", "endpoint": "/subscriptions/current", "method": "GET", "status": "PASS", "status_code": 200, "message": "Get current subscription", "error": "", "response_data": {"code": 200, "message": "success", "data": null, "timestamp": "2025-07-24 16:45:58"}}, {"timestamp": "2025-07-24T16:45:58.152642", "endpoint": "/subscriptions", "method": "POST", "status": "PASS", "status_code": 200, "message": "Create subscription", "error": "", "response_data": {"code": 200, "message": "订阅创建成功", "data": {"id": 9, "accountId": 14, "subscriptionPlanId": 1, "planName": "免费版", "planDescription": "适合个人用户，可创建1个团队", "maxSize": 1, "price": 0.0, "startDate": "2025-07-24", "endDate": null, "status": "ACTIVE", "createdAt": "2025-07-24 16:45:58", "updatedAt": "2025-07-24 16:45:58"}, "timestamp": "2025-07-24 16:45:58"}}, {"timestamp": "2025-07-24T16:45:58.177661", "endpoint": "/subscriptions/9", "method": "DELETE", "status": "PASS", "status_code": 200, "message": "Cancel subscription", "error": "", "response_data": {"code": 200, "message": "订阅取消成功", "data": null, "timestamp": "2025-07-24 16:45:58"}}, {"timestamp": "2025-07-24T16:45:58.204083", "endpoint": "/teams", "method": "GET", "status": "PASS", "status_code": 200, "message": "Get user teams", "error": "", "response_data": {"code": 200, "message": "success", "data": [{"id": 13, "name": "Updated Test Team", "description": "Updated description", "createdBy": 14, "memberCount": 1, "isCreator": true, "lastAccessTime": "2025-07-24 16:36:33", "createdAt": "2025-07-24 16:36:33", "updatedAt": "2025-07-24 16:36:33"}], "timestamp": "2025-07-24 16:45:58"}}, {"timestamp": "2025-07-24T16:45:58.225054", "endpoint": "/teams", "method": "POST", "status": "FAIL", "status_code": 400, "message": "Create new team", "error": "", "response_data": {"code": 400, "message": "已达到订阅套餐的团队创建数量限制，当前限制：1个团队", "data": null, "timestamp": "2025-07-24 16:45:58"}}, {"timestamp": "2025-07-24T16:45:58.240608", "endpoint": "/users/profile", "method": "GET", "status": "PASS", "status_code": 200, "message": "Get user profile", "error": "", "response_data": {"code": 200, "message": "success", "data": {"id": 14, "email": "<EMAIL>", "name": "Test User 1", "defaultSubscriptionPlanId": 1, "createdAt": "2025-07-24 16:32:03", "updatedAt": "2025-07-24 16:32:03"}, "timestamp": "2025-07-24 16:45:58"}}, {"timestamp": "2025-07-24T16:45:58.277288", "endpoint": "/users/profile", "method": "PUT", "status": "PASS", "status_code": 200, "message": "Update user profile", "error": "", "response_data": {"code": 200, "message": "用户资料更新成功", "data": {"id": 14, "email": "<EMAIL>", "name": "Updated Test User", "defaultSubscriptionPlanId": 1, "createdAt": "2025-07-24 16:32:03", "updatedAt": "2025-07-24 16:32:03"}, "timestamp": "2025-07-24 16:45:58"}}, {"timestamp": "2025-07-24T16:45:58.440854", "endpoint": "/users/validate-password", "method": "POST", "status": "PASS", "status_code": 200, "message": "Validate user password", "error": "", "response_data": {"code": 200, "message": "success", "data": true, "timestamp": "2025-07-24 16:45:58"}}, {"timestamp": "2025-07-24T16:45:58.602549", "endpoint": "/users/validate-password", "method": "POST", "status": "FAIL", "status_code": 200, "message": "Validate wrong password", "error": "", "response_data": {"code": 200, "message": "success", "data": false, "timestamp": "2025-07-24 16:45:58"}}, {"timestamp": "2025-07-24T16:45:58.630292", "endpoint": "/friends/request", "method": "POST", "status": "PASS", "status_code": 200, "message": "Send friend request", "error": "", "response_data": {"code": 200, "message": "success", "data": "好友请求发送成功", "timestamp": "2025-07-24 16:45:58"}}, {"timestamp": "2025-07-24T16:45:58.672660", "endpoint": "/friends/requests/sent", "method": "GET", "status": "FAIL", "status_code": 500, "message": "Get sent friend requests", "error": "", "response_data": {"code": 500, "message": "系统内部错误，请联系管理员", "data": null, "timestamp": "2025-07-24 16:45:58"}}, {"timestamp": "2025-07-24T16:45:58.723297", "endpoint": "/friends/requests/received", "method": "GET", "status": "FAIL", "status_code": 500, "message": "Get received friend requests", "error": "", "response_data": {"code": 500, "message": "系统内部错误，请联系管理员", "data": null, "timestamp": "2025-07-24 16:45:58"}}, {"timestamp": "2025-07-24T16:45:58.751234", "endpoint": "/friends/list", "method": "GET", "status": "PASS", "status_code": 200, "message": "Get friends list", "error": "", "response_data": {"code": 200, "message": "success", "data": [], "timestamp": "2025-07-24 16:45:58"}}, {"timestamp": "2025-07-24T16:45:58.771727", "endpoint": "/friends/count", "method": "GET", "status": "PASS", "status_code": 200, "message": "Get friend count", "error": "", "response_data": {"code": 200, "message": "success", "data": 0, "timestamp": "2025-07-24 16:45:58"}}, {"timestamp": "2025-07-24T16:45:58.786226", "endpoint": "/users/profile", "method": "GET", "status": "PASS", "status_code": 200, "message": "Get user 2 profile for friendship check", "error": "", "response_data": {"code": 200, "message": "success", "data": {"id": 15, "email": "<EMAIL>", "name": "Test User 2", "defaultSubscriptionPlanId": 1, "createdAt": "2025-07-24 16:32:04", "updatedAt": "2025-07-24 16:32:04"}, "timestamp": "2025-07-24 16:45:58"}}, {"timestamp": "2025-07-24T16:45:58.806538", "endpoint": "/friends/check", "method": "GET", "status": "PASS", "status_code": 200, "message": "Check friendship status", "error": "", "response_data": {"code": 200, "message": "success", "data": true, "timestamp": "2025-07-24 16:45:58"}}, {"timestamp": "2025-07-24T16:45:58.828140", "endpoint": "/friends/list", "method": "GET", "status": "PASS", "status_code": 200, "message": "Get friends list for remark test", "error": "", "response_data": {"code": 200, "message": "success", "data": [], "timestamp": "2025-07-24 16:45:58"}}, {"timestamp": "2025-07-24T16:45:58.846409", "endpoint": "/friends/request", "method": "POST", "status": "FAIL", "status_code": 404, "message": "Send friend request to non-existent user", "error": "", "response_data": {"code": 404, "message": "用户不存在", "data": null, "timestamp": "2025-07-24 16:45:58"}}]}