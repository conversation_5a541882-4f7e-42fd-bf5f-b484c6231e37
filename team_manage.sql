/*
 Navicat Premium Data Transfer

 Source Server         : localhost_3306
 Source Server Type    : MariaDB
 Source Server Version : 110402 (11.4.2-MariaDB)
 Source Host           : localhost:3306
 Source Schema         : team_manage

 Target Server Type    : MariaDB
 Target Server Version : 110402 (11.4.2-MariaDB)
 File Encoding         : 65001

 Date: 23/07/2025 18:01:13
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for account
-- ----------------------------
DROP TABLE IF EXISTS `account`;
CREATE TABLE `account`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '邮箱',
  `password_hash` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码哈希',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `default_subscription_plan_id` bigint(20) NULL DEFAULT NULL COMMENT '当前套餐ID',
  `created_at` timestamp NULL DEFAULT current_timestamp() COMMENT '注册时间',
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `email`(`email`) USING BTREE,
  INDEX `idx_email`(`email`) USING BTREE,
  INDEX `idx_subscription_plan`(`default_subscription_plan_id`) USING BTREE,
  CONSTRAINT `account_ibfk_1` FOREIGN KEY (`default_subscription_plan_id`) REFERENCES `subscription_plan` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户账户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of account
-- ----------------------------
INSERT INTO `account` VALUES (1, '<EMAIL>', '$2a$10$q9mxyE6XRqdsyElqwTZ38Oe5Cki8nzn7UVFPGtQNH9VJqg1JFyk5S', 'admin', 1, '2025-07-23 10:06:57', '2025-07-23 10:06:57');
INSERT INTO `account` VALUES (2, '<EMAIL>', '$2a$10$vNb8yNN9lVFJsyKqIC3K2OO7LlFWaVdovO78pcp3g9ZskOR8/QrBe', '更新后的用户名', 1, '2025-07-23 10:20:55', '2025-07-23 10:20:55');
INSERT INTO `account` VALUES (3, '<EMAIL>', '$2a$10$ChCYtV3VyqafUlT5eipK8OMYOIURiAZZZ370krAqC/ToTvE.1I9RW', '更新后的用户名', 1, '2025-07-23 10:24:38', '2025-07-23 10:24:38');
INSERT INTO `account` VALUES (4, '<EMAIL>', '$2a$10$ADF3FotUaqYAzknOh2GCYe/ggGuLMW/yEuMSHtPGRTqcTSAzC8gMi', '????3', 1, '2025-07-23 10:25:38', '2025-07-23 10:25:38');
INSERT INTO `account` VALUES (5, '<EMAIL>', '$2a$10$mbuY0NIlJ3zI0kMc7QAVYuBxRNni9csBJezx4uQ.afwn/WEKQ7Om.', '更新后的用户名', 1, '2025-07-23 10:26:33', '2025-07-23 10:26:33');
INSERT INTO `account` VALUES (6, '<EMAIL>', '$2a$10$upqnJlqlprFh8fej11vRJujmTRBS15hx6XaAawJaMbqwVD6iioAdK', '更新后的完整测试用户', 1, '2025-07-23 10:29:49', '2025-07-23 10:29:49');
INSERT INTO `account` VALUES (7, '<EMAIL>', '$2a$10$8jRMetPcuZ3o31O/x/hyuu7mfQ85ZhiZjBIZcKwFkcbmj2vxgsite', '更新后的完整测试用户b3u1w4', 1, '2025-07-23 10:33:37', '2025-07-23 10:33:37');
INSERT INTO `account` VALUES (8, '<EMAIL>', '$2a$10$qCWKAIBOyh7.pmLSFmbF9uDyIm9J8pMA4GfzTfEsTUabYlAUF.bM2', 'zhangsan', 2, '2025-07-23 12:07:35', '2025-07-23 12:07:35');
INSERT INTO `account` VALUES (9, '<EMAIL>', '$2a$10$Xk0or1fNmwlbGwIRgmKZjO06ab8XBUNHaFhH/ZqUomFr8xTsuwDtu', 'Test User', 1, '2025-07-23 13:58:42', '2025-07-23 13:58:42');
INSERT INTO `account` VALUES (10, '<EMAIL>', '$2a$10$J2rmSstteL8o2jgLgiJbOuPc4SaQ6EizNHS9cGXAiGzBxjgFUOjim', 'Test User', 1, '2025-07-23 14:03:11', '2025-07-23 14:03:11');
INSERT INTO `account` VALUES (11, '<EMAIL>', '$2a$10$t3P6d/pf7n8xFc53l873YuUNKplVJdN7ImrwX56DnXLPMfZ40uIdS', 'testuser', 1, '2025-07-23 14:16:00', '2025-07-23 14:16:00');
INSERT INTO `account` VALUES (12, '<EMAIL>', '$2a$10$ZFfHTQ8/qe1tTwaIQ7yz..kv0zCyBS1RrZaQHL/NdWiuMtB1QTqPG', 'testuser_n5ncrn4q', 1, '2025-07-23 14:18:38', '2025-07-23 14:18:38');
INSERT INTO `account` VALUES (13, '<EMAIL>', '$2a$10$lqoXb.HehtjUD0KZIPGv2uN7.VVfQcBCbyRZV4NrN8uoT3aRp9Mfa', 'testuser_l426tcbz', 1, '2025-07-23 14:19:18', '2025-07-23 14:19:18');

-- ----------------------------
-- Table structure for account_relation
-- ----------------------------
DROP TABLE IF EXISTS `account_relation`;
CREATE TABLE `account_relation`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '关系ID',
  `account_id` bigint(20) NOT NULL COMMENT '被邀请账号ID',
  `invited_by` bigint(20) NOT NULL COMMENT '邀请账号ID',
  `invited_at` timestamp NULL DEFAULT current_timestamp() COMMENT '邀请时间',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '是否激活',
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NULL DEFAULT 0 COMMENT '删除标记',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_relation`(`account_id`, `invited_by`, `is_deleted`) USING BTREE,
  INDEX `idx_account_active`(`account_id`, `is_active`, `is_deleted`) USING BTREE,
  INDEX `idx_inviter`(`invited_by`, `is_deleted`) USING BTREE,
  INDEX `idx_relation`(`account_id`, `invited_by`) USING BTREE,
  CONSTRAINT `account_relation_ibfk_1` FOREIGN KEY (`account_id`) REFERENCES `account` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `account_relation_ibfk_2` FOREIGN KEY (`invited_by`) REFERENCES `account` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '账号邀请关系表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of account_relation
-- ----------------------------

-- ----------------------------
-- Table structure for account_subscription
-- ----------------------------
DROP TABLE IF EXISTS `account_subscription`;
CREATE TABLE `account_subscription`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `account_id` bigint(20) NOT NULL COMMENT '用户ID',
  `subscription_plan_id` bigint(20) NOT NULL COMMENT '套餐ID',
  `start_date` date NOT NULL COMMENT '开始日期',
  `end_date` date NULL DEFAULT NULL COMMENT '结束日期',
  `status` enum('ACTIVE','EXPIRED','CANCELED') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'ACTIVE' COMMENT '订阅状态',
  `created_at` timestamp NULL DEFAULT current_timestamp() COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_account_status`(`account_id`, `status`) USING BTREE,
  INDEX `idx_date_range`(`start_date`, `end_date`) USING BTREE,
  INDEX `idx_plan`(`subscription_plan_id`) USING BTREE,
  CONSTRAINT `account_subscription_ibfk_1` FOREIGN KEY (`account_id`) REFERENCES `account` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `account_subscription_ibfk_2` FOREIGN KEY (`subscription_plan_id`) REFERENCES `subscription_plan` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户订阅历史表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of account_subscription
-- ----------------------------
INSERT INTO `account_subscription` VALUES (1, 2, 1, '2025-07-23', '2025-08-23', 'ACTIVE', '2025-07-23 10:22:49', '2025-07-23 10:22:49');
INSERT INTO `account_subscription` VALUES (2, 3, 1, '2025-07-23', '2025-08-23', 'ACTIVE', '2025-07-23 10:24:42', '2025-07-23 10:24:42');
INSERT INTO `account_subscription` VALUES (3, 5, 1, '2025-07-23', '2025-08-23', 'ACTIVE', '2025-07-23 10:26:34', '2025-07-23 10:26:34');
INSERT INTO `account_subscription` VALUES (4, 6, 1, '2025-07-23', '2025-08-23', 'CANCELED', '2025-07-23 10:29:50', '2025-07-23 10:29:50');
INSERT INTO `account_subscription` VALUES (5, 6, 1, '2025-07-23', '2025-08-23', 'ACTIVE', '2025-07-23 10:30:17', '2025-07-23 10:30:17');
INSERT INTO `account_subscription` VALUES (6, 7, 1, '2025-07-23', '2025-08-23', 'ACTIVE', '2025-07-23 10:33:37', '2025-07-23 10:33:37');
INSERT INTO `account_subscription` VALUES (7, 8, 2, '2025-07-23', '2025-08-23', 'ACTIVE', '2025-07-23 15:23:46', '2025-07-23 15:23:46');

-- ----------------------------
-- Table structure for subscription_plan
-- ----------------------------
DROP TABLE IF EXISTS `subscription_plan`;
CREATE TABLE `subscription_plan`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '套餐ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '套餐名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '套餐说明',
  `max_size` int(11) NOT NULL COMMENT '数据数量上限',
  `price` decimal(10, 2) NOT NULL COMMENT '价格(元/月)',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用',
  `created_at` timestamp NULL DEFAULT current_timestamp() COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_active`(`is_active`) USING BTREE,
  INDEX `idx_price`(`price`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '产品套餐表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of subscription_plan
-- ----------------------------
INSERT INTO `subscription_plan` VALUES (1, '免费版', '适合个人用户和小团队', 100, 0.00, 1, '2025-07-23 09:40:17', '2025-07-23 09:40:17');
INSERT INTO `subscription_plan` VALUES (2, '标准版', '适合中小企业', 1000, 99.00, 1, '2025-07-23 09:40:17', '2025-07-23 09:40:17');
INSERT INTO `subscription_plan` VALUES (3, '专业版', '适合大型企业', 10000, 299.00, 1, '2025-07-23 09:40:17', '2025-07-23 09:40:17');
INSERT INTO `subscription_plan` VALUES (4, '企业版', '无限制使用', 999999, 999.00, 1, '2025-07-23 09:40:17', '2025-07-23 09:40:17');

-- ----------------------------
-- Table structure for team
-- ----------------------------
DROP TABLE IF EXISTS `team`;
CREATE TABLE `team`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '团队ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '团队名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '团队描述',
  `created_by` bigint(20) NOT NULL COMMENT '创建人ID',
  `created_at` timestamp NULL DEFAULT current_timestamp() COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NULL DEFAULT 0 COMMENT '删除标记',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_creator`(`created_by`, `is_deleted`) USING BTREE,
  INDEX `idx_name`(`name`) USING BTREE,
  CONSTRAINT `team_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `account` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '团队信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of team
-- ----------------------------
INSERT INTO `team` VALUES (2, 'xx', 'xx', 1, '2025-07-23 10:10:49', '2025-07-23 10:10:49', 0);
INSERT INTO `team` VALUES (3, '新测试团队', '这是一个新创建的测试团队', 2, '2025-07-23 10:22:48', '2025-07-23 10:22:48', 0);
INSERT INTO `team` VALUES (4, '更新后的团队名', '更新后的团队描述', 5, '2025-07-23 10:26:33', '2025-07-23 10:26:33', 0);
INSERT INTO `team` VALUES (5, '更新后的完整测试团队', '更新后的团队描述', 6, '2025-07-23 10:29:50', '2025-07-23 10:29:50', 0);
INSERT INTO `team` VALUES (6, '完整测试团队', '这是一个完整API测试创建的团队', 6, '2025-07-23 10:30:17', '2025-07-23 10:30:17', 0);
INSERT INTO `team` VALUES (7, '更新后的完整测试团队b3u1w4', '更新后的团队描述b3u1w4', 7, '2025-07-23 10:33:37', '2025-07-23 10:33:37', 0);
INSERT INTO `team` VALUES (8, '123', '123', 8, '2025-07-23 12:07:51', '2025-07-23 12:07:51', 0);
INSERT INTO `team` VALUES (9, '测试团队_1753250591', 'API测试创建的团队', 10, '2025-07-23 14:03:11', '2025-07-23 14:03:11', 0);
INSERT INTO `team` VALUES (10, '测试团队', '这是一个测试团队', 11, '2025-07-23 14:16:01', '2025-07-23 14:16:01', 0);
INSERT INTO `team` VALUES (11, '测试团队_s33rtr', '这是一个测试团队_s33rtr', 13, '2025-07-23 14:19:18', '2025-07-23 14:19:18', 0);
INSERT INTO `team` VALUES (12, '1233', NULL, 8, '2025-07-23 15:24:33', '2025-07-23 15:24:33', 0);

-- ----------------------------
-- Table structure for team_member
-- ----------------------------
DROP TABLE IF EXISTS `team_member`;
CREATE TABLE `team_member`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '团队成员ID',
  `team_id` bigint(20) NOT NULL COMMENT '团队ID',
  `account_id` bigint(20) NOT NULL COMMENT '用户ID',
  `is_creator` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为创建者',
  `assigned_at` timestamp NULL DEFAULT current_timestamp() COMMENT '分配时间',
  `last_access_time` timestamp NULL DEFAULT NULL COMMENT '最后访问时间',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '账号状态',
  `is_deleted` tinyint(1) NULL DEFAULT 0 COMMENT '删除标记',
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_at` timestamp NULL DEFAULT current_timestamp() COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_team_member`(`team_id`, `account_id`, `is_deleted`) USING BTREE,
  INDEX `idx_team_active`(`team_id`, `is_active`, `is_deleted`) USING BTREE,
  INDEX `idx_account_team`(`account_id`, `team_id`, `is_deleted`) USING BTREE,
  INDEX `idx_creator`(`team_id`, `is_creator`, `is_deleted`) USING BTREE,
  INDEX `idx_last_access`(`account_id`, `last_access_time`) USING BTREE,
  CONSTRAINT `team_member_ibfk_1` FOREIGN KEY (`team_id`) REFERENCES `team` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `team_member_ibfk_2` FOREIGN KEY (`account_id`) REFERENCES `account` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '团队成员表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of team_member
-- ----------------------------
INSERT INTO `team_member` VALUES (1, 2, 1, 1, '2025-07-23 10:10:49', '2025-07-23 10:10:49', 1, 0, '2025-07-23 10:10:49', '2025-07-23 10:10:49');
INSERT INTO `team_member` VALUES (2, 3, 2, 1, '2025-07-23 10:22:48', '2025-07-23 10:22:48', 1, 0, '2025-07-23 10:22:48', '2025-07-23 10:22:48');
INSERT INTO `team_member` VALUES (3, 4, 5, 1, '2025-07-23 10:26:33', '2025-07-23 10:26:33', 1, 0, '2025-07-23 10:26:33', '2025-07-23 10:26:33');
INSERT INTO `team_member` VALUES (4, 5, 6, 1, '2025-07-23 10:29:50', '2025-07-23 10:29:50', 1, 0, '2025-07-23 10:29:50', '2025-07-23 10:29:50');
INSERT INTO `team_member` VALUES (5, 6, 6, 1, '2025-07-23 10:30:17', '2025-07-23 10:30:17', 1, 0, '2025-07-23 10:30:17', '2025-07-23 10:30:17');
INSERT INTO `team_member` VALUES (6, 7, 7, 1, '2025-07-23 10:33:37', '2025-07-23 10:33:38', 1, 0, '2025-07-23 10:33:38', '2025-07-23 10:33:37');
INSERT INTO `team_member` VALUES (7, 8, 8, 1, '2025-07-23 12:07:51', '2025-07-23 17:58:45', 1, 0, '2025-07-23 13:27:34', '2025-07-23 12:07:51');
INSERT INTO `team_member` VALUES (8, 9, 10, 1, '2025-07-23 14:03:11', '2025-07-23 14:03:11', 1, 0, '2025-07-23 14:03:11', '2025-07-23 14:03:11');
INSERT INTO `team_member` VALUES (9, 10, 11, 1, '2025-07-23 14:16:01', '2025-07-23 14:16:01', 1, 0, '2025-07-23 14:16:01', '2025-07-23 14:16:01');
INSERT INTO `team_member` VALUES (10, 11, 13, 1, '2025-07-23 14:19:18', '2025-07-23 14:19:19', 1, 0, '2025-07-23 14:19:19', '2025-07-23 14:19:18');
INSERT INTO `team_member` VALUES (11, 12, 8, 1, '2025-07-23 15:24:33', '2025-07-23 17:58:12', 1, 0, '2025-07-23 15:24:33', '2025-07-23 15:24:33');

SET FOREIGN_KEY_CHECKS = 1;
