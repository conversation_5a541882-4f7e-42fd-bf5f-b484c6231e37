{"summary": {"test_run_timestamp": "2025-07-24T16:32:05.380896", "total_tests": 31, "passed": 13, "failed": 18, "skipped": 0, "success_rate": "41.9%", "base_url": "http://localhost:8080/api/v1"}, "categories": {"Authentication": {"total": 12, "passed": 11, "failed": 1, "success_rate": "91.7%"}, "Team Management": {"total": 2, "passed": 0, "failed": 2, "success_rate": "0.0%"}, "User Management": {"total": 5, "passed": 1, "failed": 4, "success_rate": "20.0%"}, "Friend Management": {"total": 8, "passed": 0, "failed": 8, "success_rate": "0.0%"}, "Subscriptions": {"total": 4, "passed": 1, "failed": 3, "success_rate": "25.0%"}}, "detailed_results": [{"timestamp": "2025-07-24T16:32:03.923924", "endpoint": "/auth/register", "method": "POST", "status": "PASS", "status_code": 200, "message": "Register test user 1", "error": "", "response_data": {"code": 200, "message": "注册成功", "data": null, "timestamp": "2025-07-24 16:32:03"}}, {"timestamp": "2025-07-24T16:32:04.123036", "endpoint": "/auth/register", "method": "POST", "status": "PASS", "status_code": 200, "message": "Register test user 2", "error": "", "response_data": {"code": 200, "message": "注册成功", "data": null, "timestamp": "2025-07-24 16:32:04"}}, {"timestamp": "2025-07-24T16:32:04.318568", "endpoint": "/auth/register", "method": "POST", "status": "PASS", "status_code": 200, "message": "Register test user 3", "error": "", "response_data": {"code": 200, "message": "注册成功", "data": null, "timestamp": "2025-07-24 16:32:04"}}, {"timestamp": "2025-07-24T16:32:04.535650", "endpoint": "/auth/login", "method": "POST", "status": "PASS", "status_code": 200, "message": "Login user 1", "error": "", "response_data": {"code": 200, "message": "登录成功", "data": {"token": "eyJhbGciOiJIUzM4NCJ9.*******************************************************************************************************************************************************************************************************************************.8jhRZou_hgevIw2SesdIF_178We17eBUCwr-OWqYmKGoXKiXmdGnWCsm617jVSNz", "expiresIn": 604799470, "user": {"id": 14, "email": "<EMAIL>", "name": "Test User 1"}, "teams": [], "team": null, "teamSelectionSuccess": null}, "timestamp": "2025-07-24 16:32:04"}}, {"timestamp": "2025-07-24T16:32:04.709090", "endpoint": "/auth/login", "method": "POST", "status": "PASS", "status_code": 200, "message": "Login user 2", "error": "", "response_data": {"code": 200, "message": "登录成功", "data": {"token": "eyJhbGciOiJIUzM4NCJ9.*******************************************************************************************************************************************************************************************************************************.Kphb5u7r6FFLEiBxJYrkF6P1u_v4rnPMV-cURZgGtnuNo2QAAokV_mLREay-209E", "expiresIn": 604799296, "user": {"id": 15, "email": "<EMAIL>", "name": "Test User 2"}, "teams": [], "team": null, "teamSelectionSuccess": null}, "timestamp": "2025-07-24 16:32:04"}}, {"timestamp": "2025-07-24T16:32:04.877023", "endpoint": "/auth/login", "method": "POST", "status": "PASS", "status_code": 200, "message": "Login user 3", "error": "", "response_data": {"code": 200, "message": "登录成功", "data": {"token": "eyJhbGciOiJIUzM4NCJ9.*******************************************************************************************************************************************************************************************************************************.RQ8qjYtZ4xP5GwMsua-iaX4JuEYdma-7dxF4WX_rBbL6VXHfpGxYwVyh-fbWCNEm", "expiresIn": 604799127, "user": {"id": 16, "email": "<EMAIL>", "name": "Test User 3"}, "teams": [], "team": null, "teamSelectionSuccess": null}, "timestamp": "2025-07-24 16:32:04"}}, {"timestamp": "2025-07-24T16:32:04.915020", "endpoint": "/auth/register", "method": "POST", "status": "PASS", "status_code": 400, "message": "Register with invalid data", "error": "", "response_data": {"code": 400, "message": "参数验证失败: {password=密码长度至少8位, email=邮箱格式不正确}", "data": null, "timestamp": "2025-07-24 16:32:04"}}, {"timestamp": "2025-07-24T16:32:04.937025", "endpoint": "/auth/login", "method": "POST", "status": "PASS", "status_code": 400, "message": "Login with invalid credentials", "error": "", "response_data": {"code": 400, "message": "邮箱或密码错误", "data": null, "timestamp": "2025-07-24 16:32:04"}}, {"timestamp": "2025-07-24T16:32:04.958021", "endpoint": "/auth/validate", "method": "GET", "status": "PASS", "status_code": 200, "message": "Validate valid token", "error": "", "response_data": {"code": 200, "message": "Token验证结果", "data": true, "timestamp": "2025-07-24 16:32:04"}}, {"timestamp": "2025-07-24T16:32:04.975836", "endpoint": "/auth/validate", "method": "GET", "status": "PASS", "status_code": 401, "message": "Validate invalid token", "error": "", "response_data": {"code": 401, "message": "未认证，请先登录", "data": null, "timestamp": "2025-07-24 16:32:04"}}, {"timestamp": "2025-07-24T16:32:05.008890", "endpoint": "/auth/refresh-token", "method": "POST", "status": "PASS", "status_code": 200, "message": "Refresh valid token", "error": "", "response_data": {"code": 200, "message": "Token刷新成功", "data": {"token": "eyJhbGciOiJIUzM4NCJ9.*******************************************************************************************************************************************************************************************************************************.vzDudJAimKP5ohwcjW5wrtlF9XLs4CSDAo9XA_Rsf7cm_uDyydFEK1uH8KJCQLLf", "expiresIn": 604798997, "user": null, "teams": null, "team": null, "teamSelectionSuccess": null}, "timestamp": "2025-07-24 16:32:05"}}, {"timestamp": "2025-07-24T16:32:05.022891", "endpoint": "/auth/logout", "method": "POST", "status": "FAIL", "status_code": 401, "message": "Logout with valid token", "error": "", "response_data": {"code": 401, "message": "未认证，请先登录", "data": null, "timestamp": "2025-07-24 16:32:05"}}, {"timestamp": "2025-07-24T16:32:05.046892", "endpoint": "/plans", "method": "GET", "status": "PASS", "status_code": 200, "message": "Get subscription plans", "error": "", "response_data": {"code": 200, "message": "success", "data": [{"id": 1, "name": "免费版", "description": "适合个人用户，可创建1个团队", "maxSize": 1, "price": 0.0, "isActive": true, "createdAt": "2025-07-23 09:40:17", "updatedAt": "2025-07-24 14:57:08"}, {"id": 2, "name": "标准版", "description": "适合小团队，可创建5个团队", "maxSize": 5, "price": 99.0, "isActive": true, "createdAt": "2025-07-23 09:40:17", "updatedAt": "2025-07-24 14:57:08"}, {"id": 3, "name": "专业版", "description": "适合中型企业，可创建20个团队", "maxSize": 20, "price": 299.0, "isActive": true, "createdAt": "2025-07-23 09:40:17", "updatedAt": "2025-07-24 14:57:08"}, {"id": 4, "name": "企业版", "description": "适合大型企业，可创建无限团队", "maxSize": 999999, "price": 999.0, "isActive": true, "createdAt": "2025-07-23 09:40:17", "updatedAt": "2025-07-24 14:57:08"}], "timestamp": "2025-07-24 16:32:05"}}, {"timestamp": "2025-07-24T16:32:05.061893", "endpoint": "/subscriptions", "method": "GET", "status": "FAIL", "status_code": 401, "message": "Get user subscriptions", "error": "", "response_data": {"code": 401, "message": "未认证，请先登录", "data": null, "timestamp": "2025-07-24 16:32:05"}}, {"timestamp": "2025-07-24T16:32:05.078886", "endpoint": "/subscriptions/current", "method": "GET", "status": "FAIL", "status_code": 401, "message": "Get current subscription", "error": "", "response_data": {"code": 401, "message": "未认证，请先登录", "data": null, "timestamp": "2025-07-24 16:32:05"}}, {"timestamp": "2025-07-24T16:32:05.092828", "endpoint": "/subscriptions", "method": "POST", "status": "FAIL", "status_code": 401, "message": "Create subscription", "error": "", "response_data": {"code": 401, "message": "未认证，请先登录", "data": null, "timestamp": "2025-07-24 16:32:05"}}, {"timestamp": "2025-07-24T16:32:05.112833", "endpoint": "/teams", "method": "GET", "status": "FAIL", "status_code": 401, "message": "Get user teams", "error": "", "response_data": {"code": 401, "message": "未认证，请先登录", "data": null, "timestamp": "2025-07-24 16:32:05"}}, {"timestamp": "2025-07-24T16:32:05.126829", "endpoint": "/teams", "method": "POST", "status": "FAIL", "status_code": 401, "message": "Create new team", "error": "", "response_data": {"code": 401, "message": "未认证，请先登录", "data": null, "timestamp": "2025-07-24 16:32:05"}}, {"timestamp": "2025-07-24T16:32:05.144827", "endpoint": "/users/profile", "method": "GET", "status": "FAIL", "status_code": 401, "message": "Get user profile", "error": "", "response_data": {"code": 401, "message": "未认证，请先登录", "data": null, "timestamp": "2025-07-24 16:32:05"}}, {"timestamp": "2025-07-24T16:32:05.157825", "endpoint": "/users/profile", "method": "PUT", "status": "FAIL", "status_code": 401, "message": "Update user profile", "error": "", "response_data": {"code": 401, "message": "未认证，请先登录", "data": null, "timestamp": "2025-07-24 16:32:05"}}, {"timestamp": "2025-07-24T16:32:05.171831", "endpoint": "/users/validate-password", "method": "POST", "status": "FAIL", "status_code": 401, "message": "Validate user password", "error": "", "response_data": {"code": 401, "message": "未认证，请先登录", "data": null, "timestamp": "2025-07-24 16:32:05"}}, {"timestamp": "2025-07-24T16:32:05.186828", "endpoint": "/users/validate-password", "method": "POST", "status": "FAIL", "status_code": 401, "message": "Validate wrong password", "error": "", "response_data": {"code": 401, "message": "未认证，请先登录", "data": null, "timestamp": "2025-07-24 16:32:05"}}, {"timestamp": "2025-07-24T16:32:05.204806", "endpoint": "/friends/request", "method": "POST", "status": "FAIL", "status_code": 401, "message": "Send friend request", "error": "", "response_data": {"code": 401, "message": "未认证，请先登录", "data": null, "timestamp": "2025-07-24 16:32:05"}}, {"timestamp": "2025-07-24T16:32:05.218927", "endpoint": "/friends/requests/sent", "method": "GET", "status": "FAIL", "status_code": 401, "message": "Get sent friend requests", "error": "", "response_data": {"code": 401, "message": "未认证，请先登录", "data": null, "timestamp": "2025-07-24 16:32:05"}}, {"timestamp": "2025-07-24T16:32:05.282926", "endpoint": "/friends/requests/received", "method": "GET", "status": "FAIL", "status_code": 500, "message": "Get received friend requests", "error": "", "response_data": {"code": 500, "message": "系统内部错误，请联系管理员", "data": null, "timestamp": "2025-07-24 16:32:05"}}, {"timestamp": "2025-07-24T16:32:05.296926", "endpoint": "/friends/list", "method": "GET", "status": "FAIL", "status_code": 401, "message": "Get friends list", "error": "", "response_data": {"code": 401, "message": "未认证，请先登录", "data": null, "timestamp": "2025-07-24 16:32:05"}}, {"timestamp": "2025-07-24T16:32:05.309927", "endpoint": "/friends/count", "method": "GET", "status": "FAIL", "status_code": 401, "message": "Get friend count", "error": "", "response_data": {"code": 401, "message": "未认证，请先登录", "data": null, "timestamp": "2025-07-24 16:32:05"}}, {"timestamp": "2025-07-24T16:32:05.332897", "endpoint": "/users/profile", "method": "GET", "status": "PASS", "status_code": 200, "message": "Get user 2 profile for friendship check", "error": "", "response_data": {"code": 200, "message": "success", "data": {"id": 15, "email": "<EMAIL>", "name": "Test User 2", "defaultSubscriptionPlanId": 1, "createdAt": "2025-07-24 16:32:04", "updatedAt": "2025-07-24 16:32:04"}, "timestamp": "2025-07-24 16:32:05"}}, {"timestamp": "2025-07-24T16:32:05.345892", "endpoint": "/friends/check", "method": "GET", "status": "FAIL", "status_code": 401, "message": "Check friendship status", "error": "", "response_data": {"code": 401, "message": "未认证，请先登录", "data": null, "timestamp": "2025-07-24 16:32:05"}}, {"timestamp": "2025-07-24T16:32:05.359899", "endpoint": "/friends/list", "method": "GET", "status": "FAIL", "status_code": 401, "message": "Get friends list for remark test", "error": "", "response_data": {"code": 401, "message": "未认证，请先登录", "data": null, "timestamp": "2025-07-24 16:32:05"}}, {"timestamp": "2025-07-24T16:32:05.371891", "endpoint": "/friends/request", "method": "POST", "status": "FAIL", "status_code": 401, "message": "Send friend request to non-existent user", "error": "", "response_data": {"code": 401, "message": "未认证，请先登录", "data": null, "timestamp": "2025-07-24 16:32:05"}}]}