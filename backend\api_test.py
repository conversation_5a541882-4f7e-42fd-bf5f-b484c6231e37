#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
TeamAuth API Test Script
------------------------
This script tests all backend API endpoints for the TeamAuth project.
It validates response status codes, data structures, and handles both success and error scenarios.
"""

import requests
import json
import logging
import sys
import time
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple, Union

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("api_test_results.log"),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# API Configuration
BASE_URL = "http://localhost:8080/api/v1"
TEST_USER = {
    "email": "<EMAIL>",
    "password": "password123",
    "name": "Test User"
}
TEST_TEAM = {
    "name": "Test Team",
    "description": "Team created for API testing"
}

class ApiTestResult:
    """Class to store test results for reporting"""
    def __init__(self, endpoint: str, method: str, description: str):
        self.endpoint = endpoint
        self.method = method
        self.description = description
        self.status = "NOT RUN"
        self.status_code = None
        self.message = ""
        self.start_time = None
        self.end_time = None
        self.duration = None
    
    def start(self):
        self.start_time = time.time()
        self.status = "RUNNING"
    
    def success(self, status_code: int, message: str = ""):
        self.end_time = time.time()
        self.duration = self.end_time - self.start_time
        self.status = "PASS"
        self.status_code = status_code
        self.message = message
    
    def failure(self, status_code: int, message: str):
        self.end_time = time.time()
        self.duration = self.end_time - self.start_time
        self.status = "FAIL"
        self.status_code = status_code
        self.message = message
    
    def to_dict(self) -> Dict:
        return {
            "endpoint": self.endpoint,
            "method": self.method,
            "description": self.description,
            "status": self.status,
            "status_code": self.status_code,
            "message": self.message,
            "duration": f"{self.duration:.2f}s" if self.duration else None
        }

class TeamAuthApiTester:
    """Main API testing class for TeamAuth backend"""
    
    def __init__(self):
        self.base_url = BASE_URL
        self.user_token = None
        self.team_token = None
        self.user_id = None
        self.team_id = None
        self.test_results = []
        self.session = requests.Session()
    
    def run_test(self, endpoint: str, method: str, description: str, 
                expected_status: int = 200, data: Dict = None, 
                token: str = None, params: Dict = None) -> Tuple[bool, Dict, ApiTestResult]:
        """
        Run a single API test
        
        Args:
            endpoint: API endpoint path
            method: HTTP method (GET, POST, PUT, DELETE)
            description: Test description
            expected_status: Expected HTTP status code
            data: Request payload
            token: Auth token (if needed)
            params: URL parameters (for GET requests)
            
        Returns:
            Tuple of (success, response_data, test_result)
        """
        url = f"{self.base_url}{endpoint}"
        headers = {}
        
        if token:
            headers["Authorization"] = f"Bearer {token}"
        
        if data:
            headers["Content-Type"] = "application/json"
        
        test_result = ApiTestResult(endpoint, method, description)
        test_result.start()
        
        try:
            if method.upper() == "GET":
                response = self.session.get(url, headers=headers, params=params)
            elif method.upper() == "POST":
                response = self.session.post(url, headers=headers, json=data)
            elif method.upper() == "PUT":
                response = self.session.put(url, headers=headers, json=data)
            elif method.upper() == "DELETE":
                response = self.session.delete(url, headers=headers, json=data)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
            
            response_data = response.json() if response.text else {}
            
            if response.status_code == expected_status:
                test_result.success(response.status_code, "Test passed successfully")
                logger.info(f"PASS {description} - Status: {response.status_code}")
                return True, response_data, test_result
            else:
                error_msg = f"Expected status {expected_status}, got {response.status_code}. Response: {response_data}"
                test_result.failure(response.status_code, error_msg)
                logger.error(f"FAIL {description} - {error_msg}")
                return False, response_data, test_result
                
        except Exception as e:
            error_msg = f"Exception: {str(e)}"
            test_result.failure(0, error_msg)
            logger.error(f"FAIL {description} - {error_msg}")
            return False, {}, test_result
        finally:
            self.test_results.append(test_result)
    
    def setup(self) -> bool:
        """Setup test environment - register user and create team"""
        logger.info("Setting up test environment...")
        
        # Check if server is running
        try:
            response = self.session.get(f"{self.base_url}/plans")
            if response.status_code != 200:
                logger.error(f"Server is not responding correctly. Status: {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"Cannot connect to server: {str(e)}")
            return False
        
        # Register test user
        success, data, _ = self.run_test(
            endpoint="/auth/register",
            method="POST",
            description="Register test user",
            expected_status=200,
            data=TEST_USER
        )
        
        if not success:
            # Try logging in if registration fails (user might already exist)
            success, data, _ = self.run_test(
                endpoint="/auth/login",
                method="POST",
                description="Login with test user",
                expected_status=200,
                data={"email": TEST_USER["email"], "password": TEST_USER["password"]}
            )
            
            if not success:
                logger.error("Failed to setup test user")
                return False
        
        # Save user token and ID
        if success and data.get("data"):
            self.user_token = data["data"].get("token")
            self.user_id = data["data"].get("user", {}).get("id")
            
            # Check if user has teams
            teams = data["data"].get("teams", [])
            if teams:
                self.team_id = teams[0].get("id")
                
                # Select team
                success, data, _ = self.run_test(
                    endpoint="/auth/select-team",
                    method="POST",
                    description="Select existing team",
                    expected_status=200,
                    data={"teamId": self.team_id},
                    token=self.user_token
                )
                
                if success and data.get("data"):
                    self.team_token = data["data"].get("token")
            else:
                # Create a new team
                success, data, _ = self.run_test(
                    endpoint="/teams",
                    method="POST",
                    description="Create test team",
                    expected_status=200,
                    data=TEST_TEAM,
                    token=self.user_token
                )
                
                if success and data.get("data"):
                    self.team_id = data["data"].get("id")
                    
                    # Select the newly created team
                    success, data, _ = self.run_test(
                        endpoint="/auth/select-team",
                        method="POST",
                        description="Select created team",
                        expected_status=200,
                        data={"teamId": self.team_id},
                        token=self.user_token
                    )
                    
                    if success and data.get("data"):
                        self.team_token = data["data"].get("token")
        
        if not self.user_token or not self.team_token:
            logger.error("Failed to obtain required tokens")
            return False
            
        logger.info("Test environment setup completed successfully")
        return True

    def test_auth_endpoints(self):
        """Test authentication related endpoints"""
        logger.info("Testing authentication endpoints...")

        # Test login with valid credentials
        self.run_test(
            endpoint="/auth/login",
            method="POST",
            description="Login with valid credentials",
            expected_status=200,
            data={"email": TEST_USER["email"], "password": TEST_USER["password"]}
        )

        # Test login with invalid credentials
        self.run_test(
            endpoint="/auth/login",
            method="POST",
            description="Login with invalid credentials",
            expected_status=400,
            data={"email": TEST_USER["email"], "password": "wrongpassword"}
        )

        # Test login with invalid email format
        self.run_test(
            endpoint="/auth/login",
            method="POST",
            description="Login with invalid email format",
            expected_status=400,
            data={"email": "invalid-email", "password": TEST_USER["password"]}
        )

        # Test token validation
        self.run_test(
            endpoint="/auth/validate",
            method="GET",
            description="Validate valid token",
            expected_status=200,
            token=self.user_token
        )

        # Test token validation with invalid token
        self.run_test(
            endpoint="/auth/validate",
            method="GET",
            description="Validate invalid token",
            expected_status=200,
            token="invalid_token"
        )

        # Test refresh token
        self.run_test(
            endpoint="/auth/refresh-token",
            method="POST",
            description="Refresh valid token",
            expected_status=200,
            token=self.user_token
        )

        # Test switch team
        if self.team_id:
            self.run_test(
                endpoint="/auth/switch-team",
                method="POST",
                description="Switch to team",
                expected_status=200,
                data={"teamId": self.team_id},
                token=self.user_token
            )

        # Test clear team context
        self.run_test(
            endpoint="/auth/clear-team",
            method="POST",
            description="Clear team context",
            expected_status=200,
            token=self.team_token
        )

    def test_user_endpoints(self):
        """Test user management endpoints"""
        logger.info("Testing user management endpoints...")

        # Test get user profile
        self.run_test(
            endpoint="/users/profile",
            method="GET",
            description="Get user profile",
            expected_status=200,
            token=self.user_token
        )

        # Test update user profile
        self.run_test(
            endpoint="/users/profile",
            method="PUT",
            description="Update user profile",
            expected_status=200,
            data={"name": "Updated Test User"},
            token=self.user_token
        )

        # Test update user profile with invalid data
        self.run_test(
            endpoint="/users/profile",
            method="PUT",
            description="Update user profile with invalid data",
            expected_status=400,
            data={"name": "A" * 101},  # Name too long
            token=self.user_token
        )

        # Test legacy update profile endpoint
        self.run_test(
            endpoint="/users/profile/update",
            method="POST",
            description="Update user profile (legacy endpoint)",
            expected_status=200,
            data={"name": "Legacy Updated User"},
            token=self.user_token
        )

    def test_team_endpoints(self):
        """Test team management endpoints"""
        logger.info("Testing team management endpoints...")

        # Test get user teams
        self.run_test(
            endpoint="/teams",
            method="GET",
            description="Get user teams",
            expected_status=200,
            token=self.user_token
        )

        # Test create team
        new_team_data = {
            "name": "New Test Team",
            "description": "Another team for testing"
        }
        success, data, _ = self.run_test(
            endpoint="/teams",
            method="POST",
            description="Create new team",
            expected_status=200,
            data=new_team_data,
            token=self.user_token
        )

        # Test create team with invalid data
        self.run_test(
            endpoint="/teams",
            method="POST",
            description="Create team with invalid data",
            expected_status=400,
            data={"name": ""},  # Empty name
            token=self.user_token
        )

        # Test get current team detail (requires team token)
        self.run_test(
            endpoint="/teams/current",
            method="GET",
            description="Get current team detail",
            expected_status=200,
            token=self.team_token
        )

        # Test update current team
        self.run_test(
            endpoint="/teams/current",
            method="PUT",
            description="Update current team",
            expected_status=200,
            data={"name": "Updated Team Name", "description": "Updated description"},
            token=self.team_token
        )

        # Test get team members
        self.run_test(
            endpoint="/teams/current/members",
            method="GET",
            description="Get team members",
            expected_status=200,
            token=self.team_token,
            params={"page": 1, "size": 10}
        )

        # Test invite members
        self.run_test(
            endpoint="/teams/current/invite",
            method="POST",
            description="Invite team members",
            expected_status=200,
            data={"emails": ["<EMAIL>"]},
            token=self.team_token
        )

        # Test invite members with invalid emails
        self.run_test(
            endpoint="/teams/current/invite",
            method="POST",
            description="Invite members with invalid emails",
            expected_status=400,
            data={"emails": ["invalid-email"]},
            token=self.team_token
        )

    def test_subscription_endpoints(self):
        """Test subscription management endpoints"""
        logger.info("Testing subscription endpoints...")

        # Test get subscription plans (public endpoint)
        self.run_test(
            endpoint="/plans",
            method="GET",
            description="Get subscription plans",
            expected_status=200
        )

    def test_friend_endpoints(self):
        """Test friend management endpoints"""
        logger.info("Testing friend management endpoints...")

        # Test get friend list
        self.run_test(
            endpoint="/friends/list",
            method="GET",
            description="Get friend list",
            expected_status=200,
            token=self.user_token
        )

        # Test get received friend requests
        self.run_test(
            endpoint="/friends/requests/received",
            method="GET",
            description="Get received friend requests",
            expected_status=200,
            token=self.user_token
        )

        # Test get sent friend requests
        self.run_test(
            endpoint="/friends/requests/sent",
            method="GET",
            description="Get sent friend requests",
            expected_status=200,
            token=self.user_token
        )

        # Test search users
        self.run_test(
            endpoint="/friends/search",
            method="GET",
            description="Search users",
            expected_status=200,
            token=self.user_token,
            params={"keyword": "test"}
        )

        # Test mapper functionality
        self.run_test(
            endpoint="/friends/test/mapper",
            method="GET",
            description="Test mapper functionality",
            expected_status=200,
            token=self.user_token
        )

    def test_error_scenarios(self):
        """Test various error scenarios"""
        logger.info("Testing error scenarios...")

        # Test unauthorized access
        self.run_test(
            endpoint="/users/profile",
            method="GET",
            description="Access protected endpoint without token",
            expected_status=401
        )

        # Test access team endpoint with user token
        self.run_test(
            endpoint="/teams/current",
            method="GET",
            description="Access team endpoint with user token",
            expected_status=403,
            token=self.user_token
        )

        # Test invalid JSON payload
        try:
            response = self.session.post(
                f"{self.base_url}/auth/login",
                headers={"Content-Type": "application/json"},
                data="invalid json"
            )
            test_result = ApiTestResult("/auth/login", "POST", "Invalid JSON payload")
            test_result.start()
            if response.status_code == 400:
                test_result.success(response.status_code, "Correctly rejected invalid JSON")
                logger.info("PASS Invalid JSON payload - Status: 400")
            else:
                test_result.failure(response.status_code, f"Expected 400, got {response.status_code}")
                logger.error(f"FAIL Invalid JSON payload - Expected 400, got {response.status_code}")
            self.test_results.append(test_result)
        except Exception as e:
            test_result = ApiTestResult("/auth/login", "POST", "Invalid JSON payload")
            test_result.start()
            test_result.failure(0, f"Exception: {str(e)}")
            self.test_results.append(test_result)
            logger.error(f"❌ Invalid JSON payload - Exception: {str(e)}")

        # Test non-existent endpoint
        self.run_test(
            endpoint="/nonexistent",
            method="GET",
            description="Access non-existent endpoint",
            expected_status=404
        )

    def test_validation_scenarios(self):
        """Test input validation scenarios"""
        logger.info("Testing input validation scenarios...")

        # Test registration with missing fields
        self.run_test(
            endpoint="/auth/register",
            method="POST",
            description="Register with missing email",
            expected_status=400,
            data={"password": "password123", "name": "Test User"}
        )

        # Test registration with short password
        self.run_test(
            endpoint="/auth/register",
            method="POST",
            description="Register with short password",
            expected_status=400,
            data={"email": "<EMAIL>", "password": "123", "name": "Test User"}
        )

        # Test team creation with long name
        self.run_test(
            endpoint="/teams",
            method="POST",
            description="Create team with name too long",
            expected_status=400,
            data={"name": "A" * 101, "description": "Test"},
            token=self.user_token
        )

        # Test team invitation with too many emails
        self.run_test(
            endpoint="/teams/current/invite",
            method="POST",
            description="Invite too many members at once",
            expected_status=400,
            data={"emails": [f"user{i}@example.com" for i in range(11)]},  # Max is 10
            token=self.team_token
        )

    def run_all_tests(self) -> bool:
        """Run all API tests"""
        logger.info("Starting comprehensive API testing...")
        start_time = time.time()

        # Setup test environment
        if not self.setup():
            logger.error("Failed to setup test environment")
            return False

        # Run all test suites
        self.test_auth_endpoints()
        self.test_user_endpoints()
        self.test_team_endpoints()
        self.test_subscription_endpoints()
        self.test_friend_endpoints()
        self.test_error_scenarios()
        self.test_validation_scenarios()

        # Cleanup - logout
        self.run_test(
            endpoint="/auth/logout",
            method="POST",
            description="Logout user",
            expected_status=200,
            token=self.user_token
        )

        end_time = time.time()
        total_duration = end_time - start_time

        # Generate report
        self.generate_report(total_duration)

        # Return overall success
        failed_tests = [r for r in self.test_results if r.status == "FAIL"]
        return len(failed_tests) == 0

    def generate_report(self, total_duration: float):
        """Generate comprehensive test report"""
        logger.info("Generating test report...")

        # Calculate statistics
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r.status == "PASS"])
        failed_tests = len([r for r in self.test_results if r.status == "FAIL"])
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0

        # Create detailed report
        report = {
            "test_summary": {
                "total_tests": total_tests,
                "passed": passed_tests,
                "failed": failed_tests,
                "success_rate": f"{success_rate:.1f}%",
                "total_duration": f"{total_duration:.2f}s",
                "timestamp": datetime.now().isoformat()
            },
            "test_results": [result.to_dict() for result in self.test_results],
            "failed_tests": [result.to_dict() for result in self.test_results if result.status == "FAIL"]
        }

        # Save JSON report
        with open("api_test_report.json", "w", encoding="utf-8") as f:
            json.dump(report, f, indent=2, ensure_ascii=False)

        # Generate HTML report
        self.generate_html_report(report)

        # Print summary
        print("\n" + "="*80)
        print("API TEST SUMMARY")
        print("="*80)
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success Rate: {success_rate:.1f}%")
        print(f"Total Duration: {total_duration:.2f}s")
        print("="*80)

        if failed_tests > 0:
            print("\nFAILED TESTS:")
            print("-" * 40)
            for result in self.test_results:
                if result.status == "FAIL":
                    print(f"❌ {result.method} {result.endpoint} - {result.description}")
                    print(f"   Status: {result.status_code}, Message: {result.message}")

        print(f"\nDetailed reports saved:")
        print(f"- JSON: api_test_report.json")
        print(f"- HTML: api_test_report.html")
        print(f"- Log: api_test_results.log")

    def generate_html_report(self, report: Dict):
        """Generate HTML test report"""
        html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>TeamAuth API Test Report</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
        .summary {{ display: flex; gap: 20px; margin: 20px 0; }}
        .stat-box {{ background-color: #e8f4fd; padding: 15px; border-radius: 5px; text-align: center; }}
        .pass {{ color: green; }}
        .fail {{ color: red; }}
        table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
        .status-pass {{ background-color: #d4edda; }}
        .status-fail {{ background-color: #f8d7da; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>TeamAuth API Test Report</h1>
        <p>Generated on: {report['test_summary']['timestamp']}</p>
    </div>

    <div class="summary">
        <div class="stat-box">
            <h3>Total Tests</h3>
            <p>{report['test_summary']['total_tests']}</p>
        </div>
        <div class="stat-box">
            <h3 class="pass">Passed</h3>
            <p>{report['test_summary']['passed']}</p>
        </div>
        <div class="stat-box">
            <h3 class="fail">Failed</h3>
            <p>{report['test_summary']['failed']}</p>
        </div>
        <div class="stat-box">
            <h3>Success Rate</h3>
            <p>{report['test_summary']['success_rate']}</p>
        </div>
        <div class="stat-box">
            <h3>Duration</h3>
            <p>{report['test_summary']['total_duration']}</p>
        </div>
    </div>

    <h2>Test Results</h2>
    <table>
        <thead>
            <tr>
                <th>Method</th>
                <th>Endpoint</th>
                <th>Description</th>
                <th>Status</th>
                <th>Status Code</th>
                <th>Duration</th>
                <th>Message</th>
            </tr>
        </thead>
        <tbody>
"""

        for result in report['test_results']:
            status_class = "status-pass" if result['status'] == "PASS" else "status-fail"
            html_content += f"""
            <tr class="{status_class}">
                <td>{result['method']}</td>
                <td>{result['endpoint']}</td>
                <td>{result['description']}</td>
                <td>{result['status']}</td>
                <td>{result['status_code'] or 'N/A'}</td>
                <td>{result['duration'] or 'N/A'}</td>
                <td>{result['message']}</td>
            </tr>
"""

        html_content += """
        </tbody>
    </table>
</body>
</html>
"""

        with open("api_test_report.html", "w", encoding="utf-8") as f:
            f.write(html_content)

def main():
    """Main function to run the API tests"""
    print("TeamAuth API Test Suite")
    print("=" * 50)

    tester = TeamAuthApiTester()
    success = tester.run_all_tests()

    if success:
        print("\n🎉 All tests passed!")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed. Check the reports for details.")
        sys.exit(1)

if __name__ == "__main__":
    main()
