((typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] = (typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] || []).push([
        ['p__friend__index'],
{ "src/pages/friend/components/AddFriend.tsx": function (module, exports, __mako_require__){
/**
 * 添加好友组件
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _services = __mako_require__("src/services/index.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Title, Text, Paragraph } = _antd.Typography;
const AddFriend = ({ onAddSuccess })=>{
    _s();
    const [form] = _antd.Form.useForm();
    const [searching, setSearching] = (0, _react.useState)(false);
    const [adding, setAdding] = (0, _react.useState)(null);
    const [searchResults, setSearchResults] = (0, _react.useState)([]);
    const [searchKeyword, setSearchKeyword] = (0, _react.useState)('');
    /**
   * 搜索用户的处理函数
   *
   * 执行流程：
   * 1. 验证输入的邮箱关键词不为空
   * 2. 设置搜索状态，显示加载动画
   * 3. 调用API搜索匹配的用户
   * 4. 更新搜索结果状态
   * 5. 根据结果数量显示相应提示
   * 6. 处理错误情况
   *
   * @param values 表单提交的值，包含邮箱关键词
   */ const handleSearch = async (values)=>{
        const { email } = values;
        if (!email.trim()) {
            _antd.message.warning('请输入邮箱关键词');
            return;
        }
        try {
            setSearching(true);
            setSearchKeyword(email);
            const users = await _services.FriendService.searchUsers(email.trim());
            setSearchResults(users);
            if (users.length === 0) _antd.message.info('没有找到匹配的用户');
        } catch (error) {
            console.error('搜索用户失败:', error);
            _antd.message.error('搜索用户失败，请稍后重试');
        } finally{
            setSearching(false);
        }
    };
    /**
   * 添加好友的处理函数
   *
   * 执行流程：
   * 1. 设置添加状态，显示对应用户的加载动画
   * 2. 构造添加好友请求对象
   * 3. 调用API添加好友关系
   * 4. 从搜索结果中移除已添加的用户，避免重复添加
   * 5. 调用父组件成功回调
   * 6. 处理错误情况并显示错误消息
   *
   * @param user 要添加为好友的用户对象
   */ const handleAddFriend = async (user)=>{
        try {
            setAdding(user.id);
            const request = {
                email: user.email
            };
            await _services.FriendService.addFriend(request);
            // 从搜索结果中移除已添加的用户，避免重复添加
            setSearchResults((prev)=>prev.filter((u)=>u.id !== user.id));
            onAddSuccess();
        } catch (error) {
            console.error('添加好友失败:', error);
            _antd.message.error('添加好友失败，请稍后重试');
        } finally{
            setAdding(null);
        }
    };
    // 清空搜索结果
    const handleClearSearch = ()=>{
        setSearchResults([]);
        setSearchKeyword('');
        form.resetFields();
    };
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                title: "搜索用户",
                style: {
                    marginBottom: 24
                },
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                        form: form,
                        layout: "inline",
                        onFinish: handleSearch,
                        style: {
                            width: '100%'
                        },
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                name: "email",
                                rules: [
                                    {
                                        required: true,
                                        message: '请输入邮箱关键词'
                                    },
                                    {
                                        type: 'email',
                                        message: '请输入有效的邮箱格式'
                                    }
                                ],
                                style: {
                                    flex: 1,
                                    marginRight: 16
                                },
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                    prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MailOutlined, {}, void 0, false, {
                                        fileName: "src/pages/friend/components/AddFriend.tsx",
                                        lineNumber: 136,
                                        columnNumber: 23
                                    }, void 0),
                                    placeholder: "输入邮箱地址搜索用户",
                                    size: "large",
                                    allowClear: true
                                }, void 0, false, {
                                    fileName: "src/pages/friend/components/AddFriend.tsx",
                                    lineNumber: 135,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/friend/components/AddFriend.tsx",
                                lineNumber: 127,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                            type: "primary",
                                            htmlType: "submit",
                                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SearchOutlined, {}, void 0, false, {
                                                fileName: "src/pages/friend/components/AddFriend.tsx",
                                                lineNumber: 147,
                                                columnNumber: 23
                                            }, void 0),
                                            loading: searching,
                                            size: "large",
                                            children: "搜索"
                                        }, void 0, false, {
                                            fileName: "src/pages/friend/components/AddFriend.tsx",
                                            lineNumber: 144,
                                            columnNumber: 15
                                        }, this),
                                        searchResults.length > 0 && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                            onClick: handleClearSearch,
                                            size: "large",
                                            children: "清空"
                                        }, void 0, false, {
                                            fileName: "src/pages/friend/components/AddFriend.tsx",
                                            lineNumber: 154,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/friend/components/AddFriend.tsx",
                                    lineNumber: 143,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/friend/components/AddFriend.tsx",
                                lineNumber: 142,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/friend/components/AddFriend.tsx",
                        lineNumber: 121,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Divider, {}, void 0, false, {
                        fileName: "src/pages/friend/components/AddFriend.tsx",
                        lineNumber: 165,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Paragraph, {
                        type: "secondary",
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                strong: true,
                                children: "使用说明："
                            }, void 0, false, {
                                fileName: "src/pages/friend/components/AddFriend.tsx",
                                lineNumber: 168,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("br", {}, void 0, false, {
                                fileName: "src/pages/friend/components/AddFriend.tsx",
                                lineNumber: 169,
                                columnNumber: 11
                            }, this),
                            "• 输入完整的邮箱地址或邮箱关键词进行搜索",
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("br", {}, void 0, false, {
                                fileName: "src/pages/friend/components/AddFriend.tsx",
                                lineNumber: 171,
                                columnNumber: 11
                            }, this),
                            '• 找到目标用户后点击"添加好友"按钮',
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("br", {}, void 0, false, {
                                fileName: "src/pages/friend/components/AddFriend.tsx",
                                lineNumber: 173,
                                columnNumber: 11
                            }, this),
                            '• 添加成功后可以在"我的好友"中查看'
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/friend/components/AddFriend.tsx",
                        lineNumber: 167,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/friend/components/AddFriend.tsx",
                lineNumber: 120,
                columnNumber: 7
            }, this),
            searchKeyword && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                title: `搜索结果 - "${searchKeyword}"`,
                extra: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                    type: "secondary",
                    children: [
                        "找到 ",
                        searchResults.length,
                        " 个用户"
                    ]
                }, void 0, true, {
                    fileName: "src/pages/friend/components/AddFriend.tsx",
                    lineNumber: 183,
                    columnNumber: 13
                }, void 0),
                children: searchResults.length === 0 ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Empty, {
                    image: _antd.Empty.PRESENTED_IMAGE_SIMPLE,
                    description: "没有找到匹配的用户",
                    style: {
                        padding: '50px 0'
                    },
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                        type: "secondary",
                        children: "请尝试使用其他关键词搜索"
                    }, void 0, false, {
                        fileName: "src/pages/friend/components/AddFriend.tsx",
                        lineNumber: 194,
                        columnNumber: 15
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/friend/components/AddFriend.tsx",
                    lineNumber: 189,
                    columnNumber: 13
                }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List, {
                    dataSource: searchResults,
                    renderItem: (user)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List.Item, {
                            actions: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                    type: "primary",
                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserAddOutlined, {}, void 0, false, {
                                        fileName: "src/pages/friend/components/AddFriend.tsx",
                                        lineNumber: 207,
                                        columnNumber: 29
                                    }, void 0),
                                    loading: adding === user.id,
                                    onClick: ()=>handleAddFriend(user),
                                    children: "添加好友"
                                }, "add", false, {
                                    fileName: "src/pages/friend/components/AddFriend.tsx",
                                    lineNumber: 204,
                                    columnNumber: 21
                                }, void 0)
                            ],
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List.Item.Meta, {
                                avatar: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                                    size: 48,
                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                        fileName: "src/pages/friend/components/AddFriend.tsx",
                                        lineNumber: 219,
                                        columnNumber: 31
                                    }, void 0),
                                    style: {
                                        backgroundColor: '#52c41a'
                                    },
                                    children: user.name.charAt(0).toUpperCase()
                                }, void 0, false, {
                                    fileName: "src/pages/friend/components/AddFriend.tsx",
                                    lineNumber: 217,
                                    columnNumber: 23
                                }, void 0),
                                title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        strong: true,
                                        children: user.name
                                    }, void 0, false, {
                                        fileName: "src/pages/friend/components/AddFriend.tsx",
                                        lineNumber: 227,
                                        columnNumber: 25
                                    }, void 0)
                                }, void 0, false, {
                                    fileName: "src/pages/friend/components/AddFriend.tsx",
                                    lineNumber: 226,
                                    columnNumber: 23
                                }, void 0),
                                description: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                    direction: "vertical",
                                    size: 4,
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            type: "secondary",
                                            children: user.email
                                        }, void 0, false, {
                                            fileName: "src/pages/friend/components/AddFriend.tsx",
                                            lineNumber: 232,
                                            columnNumber: 25
                                        }, void 0),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            type: "secondary",
                                            style: {
                                                fontSize: 12
                                            },
                                            children: [
                                                "注册时间: ",
                                                new Date(user.createdAt).toLocaleDateString()
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/friend/components/AddFriend.tsx",
                                            lineNumber: 233,
                                            columnNumber: 25
                                        }, void 0)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/friend/components/AddFriend.tsx",
                                    lineNumber: 231,
                                    columnNumber: 23
                                }, void 0)
                            }, void 0, false, {
                                fileName: "src/pages/friend/components/AddFriend.tsx",
                                lineNumber: 215,
                                columnNumber: 19
                            }, void 0)
                        }, void 0, false, {
                            fileName: "src/pages/friend/components/AddFriend.tsx",
                            lineNumber: 202,
                            columnNumber: 17
                        }, void 0)
                }, void 0, false, {
                    fileName: "src/pages/friend/components/AddFriend.tsx",
                    lineNumber: 199,
                    columnNumber: 13
                }, this)
            }, void 0, false, {
                fileName: "src/pages/friend/components/AddFriend.tsx",
                lineNumber: 180,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/friend/components/AddFriend.tsx",
        lineNumber: 118,
        columnNumber: 5
    }, this);
};
_s(AddFriend, "O6bewxFRwwbn+ue0rNB4x8IUYsU=", false, function() {
    return [
        _antd.Form.useForm
    ];
});
_c = AddFriend;
var _default = AddFriend;
var _c;
$RefreshReg$(_c, "AddFriend");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/friend/components/FriendList.tsx": function (module, exports, __mako_require__){
/**
 * 好友列表组件
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _services = __mako_require__("src/services/index.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Text } = _antd.Typography;
const { Search } = _antd.Input;
const FriendList = ({ friends, loading, onRemoveFriend, onRefresh })=>{
    _s();
    const [searchKeyword, setSearchKeyword] = (0, _react.useState)('');
    const [removing, setRemoving] = (0, _react.useState)(null);
    const [remarkModalVisible, setRemarkModalVisible] = (0, _react.useState)(false);
    const [currentFriend, setCurrentFriend] = (0, _react.useState)(null);
    const [remarkForm] = _antd.Form.useForm();
    /**
   * 根据搜索关键词过滤好友列表
   *
   * 搜索范围：
   * - 好友姓名（不区分大小写）
   * - 好友邮箱（不区分大小写）
   */ const filteredFriends = friends.filter((friend)=>friend.name.toLowerCase().includes(searchKeyword.toLowerCase()) || friend.email.toLowerCase().includes(searchKeyword.toLowerCase()));
    /**
   * 删除好友的处理函数
   *
   * 执行流程：
   * 1. 设置删除状态，显示加载动画
   * 2. 调用API删除好友关系
   * 3. 调用父组件回调，刷新好友列表
   * 4. 处理错误情况并显示错误消息
   * 5. 清除删除状态
   *
   * @param friend 要删除的好友对象
   */ const handleRemoveFriend = async (friend)=>{
        try {
            setRemoving(friend.id);
            await _services.FriendService.removeFriend(friend.id);
            _antd.message.success(`已删除好友 "${friend.name}"`);
            onRemoveFriend();
        } catch (error) {
            console.error('删除好友失败:', error);
            _antd.message.error('删除好友失败，请稍后重试');
        } finally{
            setRemoving(null);
        }
    };
    /**
   * 打开备注编辑模态框
   */ const handleEditRemark = async (friend)=>{
        setCurrentFriend(friend);
        setRemarkModalVisible(true);
        try {
            // 获取当前备注
            const remark = await _services.FriendService.getFriendRemark(friend.id);
            remarkForm.setFieldsValue({
                remark
            });
        } catch (error) {
            console.error('获取好友备注失败:', error);
            remarkForm.setFieldsValue({
                remark: ''
            });
        }
    };
    /**
   * 保存好友备注
   */ const handleSaveRemark = async (values)=>{
        if (!currentFriend) return;
        try {
            await _services.FriendService.setFriendRemark({
                friendId: currentFriend.id,
                remark: values.remark
            });
            _antd.message.success('备注保存成功');
            setRemarkModalVisible(false);
            setCurrentFriend(null);
            remarkForm.resetFields();
            onRefresh();
        } catch (error) {
            console.error('保存备注失败:', error);
            _antd.message.error('保存备注失败');
        }
    };
    if (friends.length === 0 && !loading) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Empty, {
        image: _antd.Empty.PRESENTED_IMAGE_SIMPLE,
        description: "暂无好友",
        style: {
            padding: '50px 0'
        },
        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
            type: "secondary",
            children: "您还没有添加任何好友，快去添加好友吧！"
        }, void 0, false, {
            fileName: "src/pages/friend/components/FriendList.tsx",
            lineNumber: 136,
            columnNumber: 9
        }, this)
    }, void 0, false, {
        fileName: "src/pages/friend/components/FriendList.tsx",
        lineNumber: 131,
        columnNumber: 7
    }, this);
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                style: {
                    marginBottom: 16
                },
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Search, {
                    placeholder: "搜索好友姓名或邮箱",
                    allowClear: true,
                    enterButton: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SearchOutlined, {}, void 0, false, {
                        fileName: "src/pages/friend/components/FriendList.tsx",
                        lineNumber: 150,
                        columnNumber: 24
                    }, void 0),
                    size: "large",
                    value: searchKeyword,
                    onChange: (e)=>setSearchKeyword(e.target.value),
                    style: {
                        maxWidth: 400
                    }
                }, void 0, false, {
                    fileName: "src/pages/friend/components/FriendList.tsx",
                    lineNumber: 147,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/friend/components/FriendList.tsx",
                lineNumber: 146,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List, {
                loading: loading,
                dataSource: filteredFriends,
                renderItem: (friend)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List.Item, {
                        actions: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                title: "编辑备注",
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                    type: "text",
                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EditOutlined, {}, void 0, false, {
                                        fileName: "src/pages/friend/components/FriendList.tsx",
                                        lineNumber: 168,
                                        columnNumber: 25
                                    }, void 0),
                                    onClick: ()=>handleEditRemark(friend),
                                    size: "small",
                                    children: "备注"
                                }, void 0, false, {
                                    fileName: "src/pages/friend/components/FriendList.tsx",
                                    lineNumber: 166,
                                    columnNumber: 17
                                }, void 0)
                            }, "remark", false, {
                                fileName: "src/pages/friend/components/FriendList.tsx",
                                lineNumber: 165,
                                columnNumber: 15
                            }, void 0),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Popconfirm, {
                                title: "确认删除好友",
                                description: `确定要删除好友 "${friend.name}" 吗？`,
                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ExclamationCircleOutlined, {
                                    style: {
                                        color: 'red'
                                    }
                                }, void 0, false, {
                                    fileName: "src/pages/friend/components/FriendList.tsx",
                                    lineNumber: 179,
                                    columnNumber: 23
                                }, void 0),
                                onConfirm: ()=>handleRemoveFriend(friend),
                                okText: "确认删除",
                                cancelText: "取消",
                                okType: "danger",
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                    type: "text",
                                    danger: true,
                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                                        fileName: "src/pages/friend/components/FriendList.tsx",
                                        lineNumber: 188,
                                        columnNumber: 25
                                    }, void 0),
                                    loading: removing === friend.id,
                                    size: "small",
                                    children: "删除"
                                }, void 0, false, {
                                    fileName: "src/pages/friend/components/FriendList.tsx",
                                    lineNumber: 185,
                                    columnNumber: 17
                                }, void 0)
                            }, "delete", false, {
                                fileName: "src/pages/friend/components/FriendList.tsx",
                                lineNumber: 175,
                                columnNumber: 15
                            }, void 0)
                        ],
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List.Item.Meta, {
                            avatar: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                                size: 48,
                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                    fileName: "src/pages/friend/components/FriendList.tsx",
                                    lineNumber: 201,
                                    columnNumber: 25
                                }, void 0),
                                style: {
                                    backgroundColor: '#1890ff'
                                },
                                children: friend.name.charAt(0).toUpperCase()
                            }, void 0, false, {
                                fileName: "src/pages/friend/components/FriendList.tsx",
                                lineNumber: 199,
                                columnNumber: 17
                            }, void 0),
                            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                    strong: true,
                                    children: friend.name
                                }, void 0, false, {
                                    fileName: "src/pages/friend/components/FriendList.tsx",
                                    lineNumber: 209,
                                    columnNumber: 19
                                }, void 0)
                            }, void 0, false, {
                                fileName: "src/pages/friend/components/FriendList.tsx",
                                lineNumber: 208,
                                columnNumber: 17
                            }, void 0),
                            description: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                direction: "vertical",
                                size: 4,
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        type: "secondary",
                                        children: friend.email
                                    }, void 0, false, {
                                        fileName: "src/pages/friend/components/FriendList.tsx",
                                        lineNumber: 214,
                                        columnNumber: 19
                                    }, void 0),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        type: "secondary",
                                        style: {
                                            fontSize: 12
                                        },
                                        children: [
                                            "加入时间: ",
                                            new Date(friend.createdAt).toLocaleDateString()
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/friend/components/FriendList.tsx",
                                        lineNumber: 215,
                                        columnNumber: 19
                                    }, void 0)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/friend/components/FriendList.tsx",
                                lineNumber: 213,
                                columnNumber: 17
                            }, void 0)
                        }, void 0, false, {
                            fileName: "src/pages/friend/components/FriendList.tsx",
                            lineNumber: 197,
                            columnNumber: 13
                        }, void 0)
                    }, void 0, false, {
                        fileName: "src/pages/friend/components/FriendList.tsx",
                        lineNumber: 163,
                        columnNumber: 11
                    }, void 0),
                pagination: {
                    pageSize: 10,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total, range)=>`第 ${range[0]}-${range[1]} 条，共 ${total} 个好友`
                }
            }, void 0, false, {
                fileName: "src/pages/friend/components/FriendList.tsx",
                lineNumber: 159,
                columnNumber: 7
            }, this),
            filteredFriends.length === 0 && searchKeyword && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Empty, {
                image: _antd.Empty.PRESENTED_IMAGE_SIMPLE,
                description: `没有找到包含 "${searchKeyword}" 的好友`,
                style: {
                    padding: '50px 0'
                }
            }, void 0, false, {
                fileName: "src/pages/friend/components/FriendList.tsx",
                lineNumber: 233,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                title: "编辑好友备注",
                open: remarkModalVisible,
                onCancel: ()=>{
                    setRemarkModalVisible(false);
                    setCurrentFriend(null);
                    remarkForm.resetFields();
                },
                footer: null,
                width: 400,
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                    form: remarkForm,
                    layout: "vertical",
                    onFinish: handleSaveRemark,
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            label: "好友备注",
                            name: "remark",
                            rules: [
                                {
                                    max: 50,
                                    message: '备注长度不能超过50个字符'
                                }
                            ],
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input.TextArea, {
                                placeholder: "为好友添加备注...",
                                rows: 3,
                                maxLength: 50,
                                showCount: true
                            }, void 0, false, {
                                fileName: "src/pages/friend/components/FriendList.tsx",
                                lineNumber: 264,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/friend/components/FriendList.tsx",
                            lineNumber: 257,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            style: {
                                marginBottom: 0,
                                textAlign: 'right'
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        onClick: ()=>{
                                            setRemarkModalVisible(false);
                                            setCurrentFriend(null);
                                            remarkForm.resetFields();
                                        },
                                        children: "取消"
                                    }, void 0, false, {
                                        fileName: "src/pages/friend/components/FriendList.tsx",
                                        lineNumber: 273,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        type: "primary",
                                        htmlType: "submit",
                                        children: "保存"
                                    }, void 0, false, {
                                        fileName: "src/pages/friend/components/FriendList.tsx",
                                        lineNumber: 280,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/friend/components/FriendList.tsx",
                                lineNumber: 272,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/friend/components/FriendList.tsx",
                            lineNumber: 271,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/friend/components/FriendList.tsx",
                    lineNumber: 252,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/friend/components/FriendList.tsx",
                lineNumber: 241,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/friend/components/FriendList.tsx",
        lineNumber: 144,
        columnNumber: 5
    }, this);
};
_s(FriendList, "pZCu4pBRtRPtPvemt70F7VVUBTE=", false, function() {
    return [
        _antd.Form.useForm
    ];
});
_c = FriendList;
var _default = FriendList;
var _c;
$RefreshReg$(_c, "FriendList");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/friend/components/FriendRequests.tsx": function (module, exports, __mako_require__){
/**
 * 好友请求管理组件
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _services = __mako_require__("src/services/index.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Text, Title } = _antd.Typography;
const FriendRequests = ({ onRequestHandled })=>{
    _s();
    const [activeTab, setActiveTab] = (0, _react.useState)('received');
    const [loading, setLoading] = (0, _react.useState)(false);
    const [receivedRequests, setReceivedRequests] = (0, _react.useState)([]);
    const [sentRequests, setSentRequests] = (0, _react.useState)([]);
    const [processing, setProcessing] = (0, _react.useState)(null);
    (0, _react.useEffect)(()=>{
        fetchRequests();
    }, []);
    const fetchRequests = async ()=>{
        try {
            setLoading(true);
            const [received, sent] = await Promise.all([
                _services.FriendService.getReceivedFriendRequests(),
                _services.FriendService.getSentFriendRequests()
            ]);
            setReceivedRequests(received);
            setSentRequests(sent);
        } catch (error) {
            console.error('获取好友请求失败:', error);
            _antd.message.error('获取好友请求失败');
        } finally{
            setLoading(false);
        }
    };
    const handleAcceptRequest = async (requestId)=>{
        try {
            setProcessing(requestId);
            await _services.FriendService.acceptFriendRequest(requestId);
            _antd.message.success('已接受好友请求');
            fetchRequests();
            onRequestHandled();
        } catch (error) {
            console.error('接受好友请求失败:', error);
            _antd.message.error('接受好友请求失败');
        } finally{
            setProcessing(null);
        }
    };
    const handleRejectRequest = async (requestId)=>{
        try {
            setProcessing(requestId);
            await _services.FriendService.rejectFriendRequest(requestId);
            _antd.message.success('已拒绝好友请求');
            fetchRequests();
        } catch (error) {
            console.error('拒绝好友请求失败:', error);
            _antd.message.error('拒绝好友请求失败');
        } finally{
            setProcessing(null);
        }
    };
    const handleCancelRequest = async (requestId)=>{
        try {
            setProcessing(requestId);
            await _services.FriendService.cancelFriendRequest(requestId);
            _antd.message.success('已取消好友请求');
            fetchRequests();
        } catch (error) {
            console.error('取消好友请求失败:', error);
            _antd.message.error('取消好友请求失败');
        } finally{
            setProcessing(null);
        }
    };
    const renderReceivedRequests = ()=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List, {
            loading: loading,
            dataSource: receivedRequests,
            renderItem: (request)=>{
                var _request_inviter_name, _request_inviter, _request_inviter1, _request_inviter2;
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List.Item, {
                    actions: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            type: "primary",
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CheckOutlined, {}, void 0, false, {
                                fileName: "src/pages/friend/components/FriendRequests.tsx",
                                lineNumber: 128,
                                columnNumber: 21
                            }, void 0),
                            loading: processing === request.id,
                            onClick: ()=>handleAcceptRequest(request.id),
                            size: "small",
                            children: "接受"
                        }, "accept", false, {
                            fileName: "src/pages/friend/components/FriendRequests.tsx",
                            lineNumber: 125,
                            columnNumber: 13
                        }, void 0),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Popconfirm, {
                            title: "确认拒绝好友请求",
                            description: "确定要拒绝这个好友请求吗？",
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ExclamationCircleOutlined, {
                                style: {
                                    color: 'red'
                                }
                            }, void 0, false, {
                                fileName: "src/pages/friend/components/FriendRequests.tsx",
                                lineNumber: 139,
                                columnNumber: 21
                            }, void 0),
                            onConfirm: ()=>handleRejectRequest(request.id),
                            okText: "确认拒绝",
                            cancelText: "取消",
                            okType: "danger",
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                danger: true,
                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CloseOutlined, {}, void 0, false, {
                                    fileName: "src/pages/friend/components/FriendRequests.tsx",
                                    lineNumber: 147,
                                    columnNumber: 23
                                }, void 0),
                                loading: processing === request.id,
                                size: "small",
                                children: "拒绝"
                            }, void 0, false, {
                                fileName: "src/pages/friend/components/FriendRequests.tsx",
                                lineNumber: 145,
                                columnNumber: 15
                            }, void 0)
                        }, "reject", false, {
                            fileName: "src/pages/friend/components/FriendRequests.tsx",
                            lineNumber: 135,
                            columnNumber: 13
                        }, void 0)
                    ],
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List.Item.Meta, {
                        avatar: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                            size: 48,
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                fileName: "src/pages/friend/components/FriendRequests.tsx",
                                lineNumber: 160,
                                columnNumber: 23
                            }, void 0),
                            style: {
                                backgroundColor: '#1890ff'
                            },
                            children: (_request_inviter = request.inviter) === null || _request_inviter === void 0 ? void 0 : (_request_inviter_name = _request_inviter.name) === null || _request_inviter_name === void 0 ? void 0 : _request_inviter_name.charAt(0).toUpperCase()
                        }, void 0, false, {
                            fileName: "src/pages/friend/components/FriendRequests.tsx",
                            lineNumber: 158,
                            columnNumber: 15
                        }, void 0),
                        title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                strong: true,
                                children: ((_request_inviter1 = request.inviter) === null || _request_inviter1 === void 0 ? void 0 : _request_inviter1.name) || '未知用户'
                            }, void 0, false, {
                                fileName: "src/pages/friend/components/FriendRequests.tsx",
                                lineNumber: 168,
                                columnNumber: 17
                            }, void 0)
                        }, void 0, false, {
                            fileName: "src/pages/friend/components/FriendRequests.tsx",
                            lineNumber: 167,
                            columnNumber: 15
                        }, void 0),
                        description: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                            direction: "vertical",
                            size: 4,
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                    type: "secondary",
                                    children: (_request_inviter2 = request.inviter) === null || _request_inviter2 === void 0 ? void 0 : _request_inviter2.email
                                }, void 0, false, {
                                    fileName: "src/pages/friend/components/FriendRequests.tsx",
                                    lineNumber: 173,
                                    columnNumber: 17
                                }, void 0),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                    type: "secondary",
                                    style: {
                                        fontSize: 12
                                    },
                                    children: [
                                        "请求时间: ",
                                        new Date(request.requestedAt).toLocaleString()
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/friend/components/FriendRequests.tsx",
                                    lineNumber: 174,
                                    columnNumber: 17
                                }, void 0)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/friend/components/FriendRequests.tsx",
                            lineNumber: 172,
                            columnNumber: 15
                        }, void 0)
                    }, void 0, false, {
                        fileName: "src/pages/friend/components/FriendRequests.tsx",
                        lineNumber: 156,
                        columnNumber: 11
                    }, void 0)
                }, void 0, false, {
                    fileName: "src/pages/friend/components/FriendRequests.tsx",
                    lineNumber: 123,
                    columnNumber: 9
                }, void 0);
            },
            locale: {
                emptyText: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Empty, {
                    image: _antd.Empty.PRESENTED_IMAGE_SIMPLE,
                    description: "暂无收到的好友请求"
                }, void 0, false, {
                    fileName: "src/pages/friend/components/FriendRequests.tsx",
                    lineNumber: 184,
                    columnNumber: 11
                }, void 0)
            }
        }, void 0, false, {
            fileName: "src/pages/friend/components/FriendRequests.tsx",
            lineNumber: 119,
            columnNumber: 5
        }, this);
    const renderSentRequests = ()=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List, {
            loading: loading,
            dataSource: sentRequests,
            renderItem: (request)=>{
                var _request_account_name, _request_account, _request_account1, _request_account2;
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List.Item, {
                    actions: [
                        request.status === 'pending' ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Popconfirm, {
                            title: "确认取消好友请求",
                            description: "确定要取消这个好友请求吗？",
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ExclamationCircleOutlined, {
                                style: {
                                    color: 'red'
                                }
                            }, void 0, false, {
                                fileName: "src/pages/friend/components/FriendRequests.tsx",
                                lineNumber: 205,
                                columnNumber: 23
                            }, void 0),
                            onConfirm: ()=>handleCancelRequest(request.id),
                            okText: "确认取消",
                            cancelText: "取消",
                            okType: "danger",
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                danger: true,
                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CloseOutlined, {}, void 0, false, {
                                    fileName: "src/pages/friend/components/FriendRequests.tsx",
                                    lineNumber: 213,
                                    columnNumber: 25
                                }, void 0),
                                loading: processing === request.id,
                                size: "small",
                                children: "取消请求"
                            }, void 0, false, {
                                fileName: "src/pages/friend/components/FriendRequests.tsx",
                                lineNumber: 211,
                                columnNumber: 17
                            }, void 0)
                        }, "cancel", false, {
                            fileName: "src/pages/friend/components/FriendRequests.tsx",
                            lineNumber: 201,
                            columnNumber: 15
                        }, void 0) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                            type: "secondary",
                            children: request.status === 'accepted' ? '已接受' : '已拒绝'
                        }, void 0, false, {
                            fileName: "src/pages/friend/components/FriendRequests.tsx",
                            lineNumber: 221,
                            columnNumber: 15
                        }, void 0)
                    ],
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List.Item.Meta, {
                        avatar: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                            size: 48,
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                fileName: "src/pages/friend/components/FriendRequests.tsx",
                                lineNumber: 231,
                                columnNumber: 23
                            }, void 0),
                            style: {
                                backgroundColor: '#52c41a'
                            },
                            children: (_request_account = request.account) === null || _request_account === void 0 ? void 0 : (_request_account_name = _request_account.name) === null || _request_account_name === void 0 ? void 0 : _request_account_name.charAt(0).toUpperCase()
                        }, void 0, false, {
                            fileName: "src/pages/friend/components/FriendRequests.tsx",
                            lineNumber: 229,
                            columnNumber: 15
                        }, void 0),
                        title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                    strong: true,
                                    children: ((_request_account1 = request.account) === null || _request_account1 === void 0 ? void 0 : _request_account1.name) || '未知用户'
                                }, void 0, false, {
                                    fileName: "src/pages/friend/components/FriendRequests.tsx",
                                    lineNumber: 239,
                                    columnNumber: 17
                                }, void 0),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Badge, {
                                    status: request.status === 'pending' ? 'processing' : request.status === 'accepted' ? 'success' : 'error',
                                    text: request.status === 'pending' ? '待处理' : request.status === 'accepted' ? '已接受' : '已拒绝'
                                }, void 0, false, {
                                    fileName: "src/pages/friend/components/FriendRequests.tsx",
                                    lineNumber: 240,
                                    columnNumber: 17
                                }, void 0)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/friend/components/FriendRequests.tsx",
                            lineNumber: 238,
                            columnNumber: 15
                        }, void 0),
                        description: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                            direction: "vertical",
                            size: 4,
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                    type: "secondary",
                                    children: (_request_account2 = request.account) === null || _request_account2 === void 0 ? void 0 : _request_account2.email
                                }, void 0, false, {
                                    fileName: "src/pages/friend/components/FriendRequests.tsx",
                                    lineNumber: 254,
                                    columnNumber: 17
                                }, void 0),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                    type: "secondary",
                                    style: {
                                        fontSize: 12
                                    },
                                    children: [
                                        "发送时间: ",
                                        new Date(request.invitedAt).toLocaleString()
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/friend/components/FriendRequests.tsx",
                                    lineNumber: 255,
                                    columnNumber: 17
                                }, void 0)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/friend/components/FriendRequests.tsx",
                            lineNumber: 253,
                            columnNumber: 15
                        }, void 0)
                    }, void 0, false, {
                        fileName: "src/pages/friend/components/FriendRequests.tsx",
                        lineNumber: 227,
                        columnNumber: 11
                    }, void 0)
                }, void 0, false, {
                    fileName: "src/pages/friend/components/FriendRequests.tsx",
                    lineNumber: 198,
                    columnNumber: 9
                }, void 0);
            },
            locale: {
                emptyText: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Empty, {
                    image: _antd.Empty.PRESENTED_IMAGE_SIMPLE,
                    description: "暂无发送的好友请求"
                }, void 0, false, {
                    fileName: "src/pages/friend/components/FriendRequests.tsx",
                    lineNumber: 265,
                    columnNumber: 11
                }, void 0)
            }
        }, void 0, false, {
            fileName: "src/pages/friend/components/FriendRequests.tsx",
            lineNumber: 194,
            columnNumber: 5
        }, this);
    const tabItems = [
        {
            key: 'received',
            label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.InboxOutlined, {}, void 0, false, {
                        fileName: "src/pages/friend/components/FriendRequests.tsx",
                        lineNumber: 279,
                        columnNumber: 11
                    }, this),
                    "收到的请求",
                    receivedRequests.length > 0 && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Badge, {
                        count: receivedRequests.length,
                        size: "small"
                    }, void 0, false, {
                        fileName: "src/pages/friend/components/FriendRequests.tsx",
                        lineNumber: 282,
                        columnNumber: 13
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/friend/components/FriendRequests.tsx",
                lineNumber: 278,
                columnNumber: 9
            }, this),
            children: renderReceivedRequests()
        },
        {
            key: 'sent',
            label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SendOutlined, {}, void 0, false, {
                        fileName: "src/pages/friend/components/FriendRequests.tsx",
                        lineNumber: 292,
                        columnNumber: 11
                    }, this),
                    "发送的请求",
                    sentRequests.length > 0 && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Badge, {
                        count: sentRequests.length,
                        size: "small"
                    }, void 0, false, {
                        fileName: "src/pages/friend/components/FriendRequests.tsx",
                        lineNumber: 295,
                        columnNumber: 13
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/friend/components/FriendRequests.tsx",
                lineNumber: 291,
                columnNumber: 9
            }, this),
            children: renderSentRequests()
        }
    ];
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
        title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
            children: [
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.InboxOutlined, {}, void 0, false, {
                    fileName: "src/pages/friend/components/FriendRequests.tsx",
                    lineNumber: 307,
                    columnNumber: 11
                }, void 0),
                "好友请求管理"
            ]
        }, void 0, true, {
            fileName: "src/pages/friend/components/FriendRequests.tsx",
            lineNumber: 306,
            columnNumber: 9
        }, void 0),
        extra: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
            onClick: fetchRequests,
            loading: loading,
            children: "刷新"
        }, void 0, false, {
            fileName: "src/pages/friend/components/FriendRequests.tsx",
            lineNumber: 312,
            columnNumber: 9
        }, void 0),
        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tabs, {
            activeKey: activeTab,
            onChange: setActiveTab,
            items: tabItems
        }, void 0, false, {
            fileName: "src/pages/friend/components/FriendRequests.tsx",
            lineNumber: 317,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "src/pages/friend/components/FriendRequests.tsx",
        lineNumber: 304,
        columnNumber: 5
    }, this);
};
_s(FriendRequests, "IKk9bTwJpxkdqiFlu3aDidJit1k=");
_c = FriendRequests;
var _default = FriendRequests;
var _c;
$RefreshReg$(_c, "FriendRequests");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/friend/index.tsx": function (module, exports, __mako_require__){
/**
 * 好友管理页面
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
var _services = __mako_require__("src/services/index.ts");
var _FriendList = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/friend/components/FriendList.tsx"));
var _AddFriend = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/friend/components/AddFriend.tsx"));
var _FriendRequests = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/friend/components/FriendRequests.tsx"));
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Title } = _antd.Typography;
const FriendManagePage = ()=>{
    _s();
    const [activeTab, setActiveTab] = (0, _react.useState)('list');
    const [loading, setLoading] = (0, _react.useState)(false);
    const [friends, setFriends] = (0, _react.useState)([]);
    const [friendCount, setFriendCount] = (0, _react.useState)(0);
    /**
   * 获取好友列表和好友数量
   *
   * 功能说明：
   * 1. 并发请求好友列表和好友数量，提高加载效率
   * 2. 更新组件状态，触发UI重新渲染
   * 3. 统一的错误处理和加载状态管理
   */ const fetchFriends = async ()=>{
        try {
            setLoading(true);
            // 并发请求好友列表和数量，提高性能
            const [friendList, count] = await Promise.all([
                _services.FriendService.getFriends(),
                _services.FriendService.getFriendCount()
            ]);
            setFriends(friendList);
            setFriendCount(count);
        } catch (error) {
            console.error('获取好友列表失败:', error);
            _antd.message.error('获取好友列表失败');
        } finally{
            setLoading(false);
        }
    };
    (0, _react.useEffect)(()=>{
        fetchFriends();
    }, []);
    /**
   * 添加好友成功的回调处理
   *
   * 执行步骤：
   * 1. 显示成功提示消息
   * 2. 刷新好友列表数据
   * 3. 自动切换到好友列表标签页，方便用户查看新添加的好友
   */ const handleAddFriendSuccess = ()=>{
        _antd.message.success('好友添加成功');
        fetchFriends(); // 刷新好友列表
        setActiveTab('list'); // 切换到好友列表
    };
    /**
   * 删除好友成功的回调处理
   *
   * 执行步骤：
   * 1. 显示成功提示消息
   * 2. 刷新好友列表数据，移除已删除的好友
   */ const handleRemoveFriendSuccess = ()=>{
        _antd.message.success('好友删除成功');
        fetchFriends(); // 刷新好友列表
    };
    const tabItems = [
        {
            key: 'list',
            label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UnorderedListOutlined, {}, void 0, false, {
                        fileName: "src/pages/friend/index.tsx",
                        lineNumber: 98,
                        columnNumber: 11
                    }, this),
                    "我的好友 (",
                    friendCount,
                    ")"
                ]
            }, void 0, true, {
                fileName: "src/pages/friend/index.tsx",
                lineNumber: 97,
                columnNumber: 9
            }, this),
            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_FriendList.default, {
                friends: friends,
                loading: loading,
                onRemoveFriend: handleRemoveFriendSuccess,
                onRefresh: fetchFriends
            }, void 0, false, {
                fileName: "src/pages/friend/index.tsx",
                lineNumber: 103,
                columnNumber: 9
            }, this)
        },
        {
            key: 'add',
            label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserAddOutlined, {}, void 0, false, {
                        fileName: "src/pages/friend/index.tsx",
                        lineNumber: 115,
                        columnNumber: 11
                    }, this),
                    "添加好友"
                ]
            }, void 0, true, {
                fileName: "src/pages/friend/index.tsx",
                lineNumber: 114,
                columnNumber: 9
            }, this),
            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_AddFriend.default, {
                onAddSuccess: handleAddFriendSuccess
            }, void 0, false, {
                fileName: "src/pages/friend/index.tsx",
                lineNumber: 120,
                columnNumber: 9
            }, this)
        },
        {
            key: 'requests',
            label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.InboxOutlined, {}, void 0, false, {
                        fileName: "src/pages/friend/index.tsx",
                        lineNumber: 129,
                        columnNumber: 11
                    }, this),
                    "好友请求"
                ]
            }, void 0, true, {
                fileName: "src/pages/friend/index.tsx",
                lineNumber: 128,
                columnNumber: 9
            }, this),
            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_FriendRequests.default, {
                onRequestHandled: fetchFriends
            }, void 0, false, {
                fileName: "src/pages/friend/index.tsx",
                lineNumber: 134,
                columnNumber: 9
            }, this)
        }
    ];
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
        title: "好友管理",
        subTitle: "管理您的好友关系，邀请好友加入团队",
        extra: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                onClick: fetchFriends,
                loading: loading,
                children: "刷新"
            }, "refresh", false, {
                fileName: "src/pages/friend/index.tsx",
                lineNumber: 146,
                columnNumber: 9
            }, void 0)
        ],
        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tabs, {
                activeKey: activeTab,
                onChange: setActiveTab,
                items: tabItems,
                size: "large"
            }, void 0, false, {
                fileName: "src/pages/friend/index.tsx",
                lineNumber: 156,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "src/pages/friend/index.tsx",
            lineNumber: 155,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "src/pages/friend/index.tsx",
        lineNumber: 142,
        columnNumber: 5
    }, this);
};
_s(FriendManagePage, "W4+eDAOWP/0vJ1CKtArxCusFdhE=");
_c = FriendManagePage;
var _default = FriendManagePage;
var _c;
$RefreshReg$(_c, "FriendManagePage");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
 }]);
//# sourceMappingURL=p__friend__index-async.js.map