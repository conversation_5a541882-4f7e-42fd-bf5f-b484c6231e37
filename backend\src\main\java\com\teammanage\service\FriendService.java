package com.teammanage.service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.teammanage.entity.Account;
import com.teammanage.entity.AccountRelation;
import com.teammanage.entity.FriendStatus;
import com.teammanage.exception.BusinessException;
import com.teammanage.exception.ResourceNotFoundException;
import com.teammanage.mapper.AccountMapper;
import com.teammanage.mapper.AccountRelationMapper;

/**
 * 好友关系管理服务
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class FriendService {

    private static final Logger log = LoggerFactory.getLogger(FriendService.class);

    @Autowired
    private AccountRelationMapper accountRelationMapper;

    @Autowired
    private AccountMapper accountMapper;

    /**
     * 发送好友请求
     *
     * 业务逻辑：
     * 1. 根据邮箱查找目标用户，如果不存在则抛出异常
     * 2. 验证不能添加自己为好友
     * 3. 检查是否已经存在好友关系或待处理的请求，避免重复添加
     * 4. 创建新的好友请求记录到account_relation表，状态为pending
     * 5. 记录操作日志
     *
     * @param currentUserId 当前用户ID
     * @param friendEmail 好友邮箱地址
     * @throws ResourceNotFoundException 当目标用户不存在时
     * @throws BusinessException 当尝试添加自己或已存在好友关系时
     */
    @Transactional
    public void sendFriendRequest(Long currentUserId, String friendEmail) {
        // 查找好友用户
        Account friendAccount = accountMapper.findByEmail(friendEmail);
        if (friendAccount == null) {
            throw new ResourceNotFoundException("用户不存在");
        }

        Long friendId = friendAccount.getId();

        // 不能添加自己为好友
        if (currentUserId.equals(friendId)) {
            throw new BusinessException("不能添加自己为好友");
        }

        // 检查是否已经存在好友关系或待处理的请求
        if (accountRelationMapper.existsRelation(friendId, currentUserId)) {
            throw new BusinessException("已经是好友关系或存在待处理的请求");
        }

        // 创建好友请求记录
        AccountRelation relation = new AccountRelation();
        relation.setAccountId(friendId);
        relation.setInvitedBy(currentUserId);
        relation.setInvitedAt(LocalDateTime.now());
        relation.setRequestedAt(LocalDateTime.now());
        relation.setStatus(FriendStatus.PENDING);
        relation.setIsActive(false); // 请求状态下设为false，接受后设为true
        relation.setIsDeleted(false);

        accountRelationMapper.insert(relation);

        log.info("发送好友请求成功: userId={}, friendId={}, friendEmail={}",
                currentUserId, friendId, friendEmail);
    }

    /**
     * 兼容旧接口的添加好友方法
     * @deprecated 使用 sendFriendRequest 替代
     */
    @Deprecated
    @Transactional
    public void addFriend(Long currentUserId, String friendEmail) {
        sendFriendRequest(currentUserId, friendEmail);
    }

    /**
     * 接受好友请求
     *
     * @param currentUserId 当前用户ID
     * @param requestId 好友请求ID
     */
    @Transactional
    public void acceptFriendRequest(Long currentUserId, Long requestId) {
        AccountRelation relation = accountRelationMapper.selectById(requestId);
        if (relation == null || relation.getIsDeleted()) {
            throw new ResourceNotFoundException("好友请求不存在");
        }

        // 验证当前用户是被邀请方
        if (!currentUserId.equals(relation.getAccountId())) {
            throw new BusinessException("无权处理此好友请求");
        }

        // 验证请求状态
        if (relation.getStatus() != FriendStatus.PENDING) {
            throw new BusinessException("好友请求已处理");
        }

        // 更新请求状态为已接受
        relation.setStatus(FriendStatus.ACCEPTED);
        relation.setIsActive(true);
        accountRelationMapper.updateById(relation);

        log.info("接受好友请求成功: userId={}, requestId={}, invitedBy={}",
                currentUserId, requestId, relation.getInvitedBy());
    }

    /**
     * 拒绝好友请求
     *
     * @param currentUserId 当前用户ID
     * @param requestId 好友请求ID
     */
    @Transactional
    public void rejectFriendRequest(Long currentUserId, Long requestId) {
        AccountRelation relation = accountRelationMapper.selectById(requestId);
        if (relation == null || relation.getIsDeleted()) {
            throw new ResourceNotFoundException("好友请求不存在");
        }

        // 验证当前用户是被邀请方
        if (!currentUserId.equals(relation.getAccountId())) {
            throw new BusinessException("无权处理此好友请求");
        }

        // 验证请求状态
        if (relation.getStatus() != FriendStatus.PENDING) {
            throw new BusinessException("好友请求已处理");
        }

        // 更新请求状态为已拒绝
        relation.setStatus(FriendStatus.REJECTED);
        relation.setIsActive(false);
        accountRelationMapper.updateById(relation);

        log.info("拒绝好友请求成功: userId={}, requestId={}, invitedBy={}",
                currentUserId, requestId, relation.getInvitedBy());
    }

    /**
     * 取消好友请求
     *
     * @param currentUserId 当前用户ID
     * @param requestId 好友请求ID
     */
    @Transactional
    public void cancelFriendRequest(Long currentUserId, Long requestId) {
        AccountRelation relation = accountRelationMapper.selectById(requestId);
        if (relation == null || relation.getIsDeleted()) {
            throw new ResourceNotFoundException("好友请求不存在");
        }

        // 验证当前用户是发送方
        if (!currentUserId.equals(relation.getInvitedBy())) {
            throw new BusinessException("无权取消此好友请求");
        }

        // 验证请求状态
        if (relation.getStatus() != FriendStatus.PENDING) {
            throw new BusinessException("好友请求已处理，无法取消");
        }

        // 软删除请求记录
        relation.setIsDeleted(true);
        relation.setIsActive(false);
        accountRelationMapper.updateById(relation);

        log.info("取消好友请求成功: userId={}, requestId={}, accountId={}",
                currentUserId, requestId, relation.getAccountId());
    }

    /**
     * 删除好友关系
     * 
     * @param currentUserId 当前用户ID
     * @param friendId 好友ID
     */
    @Transactional
    public void removeFriend(Long currentUserId, Long friendId) {
        // 查找好友关系（双向查找）
        QueryWrapper<AccountRelation> queryWrapper = new QueryWrapper<>();
        queryWrapper.and(wrapper -> 
            wrapper.and(w -> w.eq("account_id", friendId).eq("invited_by", currentUserId))
                   .or(w -> w.eq("account_id", currentUserId).eq("invited_by", friendId))
        ).eq("is_deleted", false);

        List<AccountRelation> relations = accountRelationMapper.selectList(queryWrapper);
        if (relations.isEmpty()) {
            throw new ResourceNotFoundException("好友关系不存在");
        }

        // 软删除所有相关的好友关系记录
        for (AccountRelation relation : relations) {
            relation.setIsDeleted(true);
            relation.setIsActive(false);
            accountRelationMapper.updateById(relation);
        }

        log.info("删除好友成功: userId={}, friendId={}, relationCount={}", 
                currentUserId, friendId, relations.size());
    }

    /**
     * 获取好友列表（只返回已接受的好友关系）
     *
     * @param currentUserId 当前用户ID
     * @return 好友列表
     */
    public List<Account> getFriends(Long currentUserId) {
        // 查找当前用户邀请的已接受好友
        QueryWrapper<AccountRelation> invitedQuery = new QueryWrapper<>();
        invitedQuery.eq("invited_by", currentUserId)
                   .eq("status", FriendStatus.ACCEPTED.getCode())
                   .eq("is_deleted", false);
        List<AccountRelation> invitedRelations = accountRelationMapper.selectList(invitedQuery);

        // 查找邀请当前用户的已接受好友
        QueryWrapper<AccountRelation> inviterQuery = new QueryWrapper<>();
        inviterQuery.eq("account_id", currentUserId)
                   .eq("status", FriendStatus.ACCEPTED.getCode())
                   .eq("is_deleted", false);
        List<AccountRelation> inviterRelations = accountRelationMapper.selectList(inviterQuery);

        // 合并好友ID列表
        List<Long> friendIds = invitedRelations.stream()
                .map(AccountRelation::getAccountId)
                .collect(Collectors.toList());

        friendIds.addAll(inviterRelations.stream()
                .map(AccountRelation::getInvitedBy)
                .collect(Collectors.toList()));

        // 去重并查询好友信息
        List<Long> uniqueFriendIds = friendIds.stream().distinct().collect(Collectors.toList());

        if (uniqueFriendIds.isEmpty()) {
            return List.of();
        }

        // 批量查询好友信息
        QueryWrapper<Account> accountQueryWrapper = new QueryWrapper<>();
        accountQueryWrapper.in("id", uniqueFriendIds);
        List<Account> friends = accountMapper.selectList(accountQueryWrapper);

        log.debug("获取好友列表: userId={}, friendCount={}", currentUserId, friends.size());

        return friends;
    }

    /**
     * 获取收到的好友请求列表
     *
     * @param currentUserId 当前用户ID
     * @return 好友请求列表
     */
    public List<AccountRelation> getReceivedFriendRequests(Long currentUserId) {
        QueryWrapper<AccountRelation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("account_id", currentUserId)
                   .eq("status", FriendStatus.PENDING.getCode())
                   .eq("is_deleted", false)
                   .orderByDesc("requested_at");

        List<AccountRelation> requests = accountRelationMapper.selectList(queryWrapper);

        log.debug("获取收到的好友请求: userId={}, requestCount={}", currentUserId, requests.size());

        return requests;
    }

    /**
     * 获取发送的好友请求列表
     *
     * @param currentUserId 当前用户ID
     * @return 好友请求列表
     */
    public List<AccountRelation> getSentFriendRequests(Long currentUserId) {
        QueryWrapper<AccountRelation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("invited_by", currentUserId)
                   .eq("status", FriendStatus.PENDING.getCode())
                   .eq("is_deleted", false)
                   .orderByDesc("requested_at");

        List<AccountRelation> requests = accountRelationMapper.selectList(queryWrapper);

        log.debug("获取发送的好友请求: userId={}, requestCount={}", currentUserId, requests.size());

        return requests;
    }

    /**
     * 检查是否为好友关系
     * 
     * @param userId1 用户1 ID
     * @param userId2 用户2 ID
     * @return 是否为好友
     */
    public boolean isFriend(Long userId1, Long userId2) {
        return accountRelationMapper.existsRelation(userId1, userId2) || 
               accountRelationMapper.existsRelation(userId2, userId1);
    }

    /**
     * 获取好友数量
     *
     * @param currentUserId 当前用户ID
     * @return 好友数量
     */
    public int getFriendCount(Long currentUserId) {
        return getFriends(currentUserId).size();
    }

    /**
     * 设置好友备注
     *
     * @param currentUserId 当前用户ID
     * @param friendId 好友ID
     * @param remark 备注内容
     */
    @Transactional
    public void setFriendRemark(Long currentUserId, Long friendId, String remark) {
        // 查找好友关系（双向查找）
        QueryWrapper<AccountRelation> queryWrapper = new QueryWrapper<>();
        queryWrapper.and(wrapper ->
            wrapper.and(w -> w.eq("account_id", friendId).eq("invited_by", currentUserId))
                   .or(w -> w.eq("account_id", currentUserId).eq("invited_by", friendId))
        ).eq("status", FriendStatus.ACCEPTED.getCode())
         .eq("is_deleted", false);

        List<AccountRelation> relations = accountRelationMapper.selectList(queryWrapper);
        if (relations.isEmpty()) {
            throw new ResourceNotFoundException("好友关系不存在");
        }

        // 找到当前用户作为邀请方或被邀请方的关系记录
        AccountRelation targetRelation = null;
        for (AccountRelation relation : relations) {
            if (relation.getInvitedBy().equals(currentUserId) || relation.getAccountId().equals(currentUserId)) {
                targetRelation = relation;
                break;
            }
        }

        if (targetRelation != null) {
            targetRelation.setRemark(remark);
            accountRelationMapper.updateById(targetRelation);

            log.info("设置好友备注成功: userId={}, friendId={}, remark={}",
                    currentUserId, friendId, remark);
        }
    }

    /**
     * 获取好友备注
     *
     * @param currentUserId 当前用户ID
     * @param friendId 好友ID
     * @return 备注内容
     */
    public String getFriendRemark(Long currentUserId, Long friendId) {
        // 查找好友关系（双向查找）
        QueryWrapper<AccountRelation> queryWrapper = new QueryWrapper<>();
        queryWrapper.and(wrapper ->
            wrapper.and(w -> w.eq("account_id", friendId).eq("invited_by", currentUserId))
                   .or(w -> w.eq("account_id", currentUserId).eq("invited_by", friendId))
        ).eq("status", FriendStatus.ACCEPTED.getCode())
         .eq("is_deleted", false);

        List<AccountRelation> relations = accountRelationMapper.selectList(queryWrapper);
        if (relations.isEmpty()) {
            return null;
        }

        // 找到当前用户作为邀请方或被邀请方的关系记录
        for (AccountRelation relation : relations) {
            if (relation.getInvitedBy().equals(currentUserId) || relation.getAccountId().equals(currentUserId)) {
                return relation.getRemark();
            }
        }

        return null;
    }
}
