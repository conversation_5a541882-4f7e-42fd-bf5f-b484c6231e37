2025-07-24 16:36:32,546 - INFO - Starting comprehensive API tests...
2025-07-24 16:36:32,547 - INFO - Setting up test data...
2025-07-24 16:36:32,597 - INFO - [FAIL] POST /auth/register - FAIL: Register test user 1
2025-07-24 16:36:32,780 - INFO - [PASS] POST /auth/login - PASS: Login existing user 1
2025-07-24 16:36:32,781 - INFO - Logged in existing user: <EMAIL>
2025-07-24 16:36:32,797 - INFO - [FAIL] POST /auth/register - FAIL: Register test user 2
2025-07-24 16:36:32,965 - INFO - [PASS] POST /auth/login - PASS: Login existing user 2
2025-07-24 16:36:32,966 - INFO - Logged in existing user: <EMAIL>
2025-07-24 16:36:32,981 - INFO - [FAIL] POST /auth/register - FAIL: Register test user 3
2025-07-24 16:36:33,150 - INFO - [PASS] POST /auth/login - PASS: Login existing user 3
2025-07-24 16:36:33,151 - INFO - Logged in existing user: <EMAIL>
2025-07-24 16:36:33,151 - INFO - Setup complete. Tokens obtained: ['user_1', 'user_2', 'user_3']
2025-07-24 16:36:33,152 - INFO - Testing Authentication Endpoints...
2025-07-24 16:36:33,162 - INFO - [PASS] POST /auth/register - PASS: Register with invalid data
2025-07-24 16:36:33,180 - INFO - [PASS] POST /auth/login - PASS: Login with invalid credentials
2025-07-24 16:36:33,195 - INFO - [PASS] GET /auth/validate - PASS: Validate valid token
2025-07-24 16:36:33,206 - INFO - [PASS] GET /auth/validate - PASS: Validate invalid token
2025-07-24 16:36:33,233 - INFO - [PASS] POST /auth/refresh-token - PASS: Refresh valid token
2025-07-24 16:36:33,405 - INFO - [PASS] POST /auth/login - PASS: Login user 1 for backup token
2025-07-24 16:36:33,415 - INFO - [FAIL] POST /auth/logout - FAIL: Logout with valid token
2025-07-24 16:36:33,415 - INFO - Testing Subscription Endpoints...
2025-07-24 16:36:33,429 - INFO - [PASS] GET /plans - PASS: Get subscription plans
2025-07-24 16:36:33,444 - INFO - [PASS] GET /subscriptions - PASS: Get user subscriptions
2025-07-24 16:36:33,471 - INFO - [PASS] GET /subscriptions/current - PASS: Get current subscription
2025-07-24 16:36:33,541 - INFO - [PASS] POST /subscriptions - PASS: Create subscription
2025-07-24 16:36:33,579 - INFO - [PASS] DELETE /subscriptions/8 - PASS: Cancel subscription
2025-07-24 16:36:33,580 - INFO - Testing Team Management Endpoints...
2025-07-24 16:36:33,597 - INFO - [PASS] GET /teams - PASS: Get user teams
2025-07-24 16:36:33,663 - INFO - [PASS] POST /teams - PASS: Create new team
2025-07-24 16:36:33,711 - INFO - [PASS] POST /auth/select-team - PASS: Select created team
2025-07-24 16:36:33,735 - INFO - [PASS] GET /teams/current - PASS: Get current team details
2025-07-24 16:36:33,795 - INFO - [PASS] PUT /teams/current - PASS: Update team information
2025-07-24 16:36:33,835 - INFO - [PASS] GET /teams/current/members - PASS: Get team members
2025-07-24 16:36:33,875 - INFO - [PASS] POST /teams/current/members/invite - PASS: Invite team members
2025-07-24 16:36:33,876 - INFO - Testing User Management Endpoints...
2025-07-24 16:36:33,885 - INFO - [FAIL] GET /users/profile - FAIL: Get user profile
2025-07-24 16:36:33,894 - INFO - [FAIL] PUT /users/profile - FAIL: Update user profile
2025-07-24 16:36:33,901 - INFO - [FAIL] POST /users/validate-password - FAIL: Validate user password
2025-07-24 16:36:33,914 - INFO - [FAIL] POST /users/validate-password - FAIL: Validate wrong password
2025-07-24 16:36:33,915 - INFO - Testing Friend Management Endpoints...
2025-07-24 16:36:33,924 - INFO - [FAIL] POST /friends/request - FAIL: Send friend request
2025-07-24 16:36:33,932 - INFO - [FAIL] GET /friends/requests/sent - FAIL: Get sent friend requests
2025-07-24 16:36:33,982 - INFO - [FAIL] GET /friends/requests/received - FAIL: Get received friend requests
2025-07-24 16:36:33,991 - INFO - [FAIL] GET /friends/list - FAIL: Get friends list
2025-07-24 16:36:34,000 - INFO - [FAIL] GET /friends/count - FAIL: Get friend count
2025-07-24 16:36:34,016 - INFO - [PASS] GET /users/profile - PASS: Get user 2 profile for friendship check
2025-07-24 16:36:34,030 - INFO - [FAIL] GET /friends/check - FAIL: Check friendship status
2025-07-24 16:36:34,040 - INFO - [FAIL] GET /friends/list - FAIL: Get friends list for remark test
2025-07-24 16:36:34,049 - INFO - [FAIL] POST /friends/request - FAIL: Send friend request to non-existent user
2025-07-24 16:36:34,049 - INFO - Generating test report...
2025-07-24 16:36:34,073 - INFO - All tests completed in 1.53 seconds
2025-07-24 16:45:57,200 - INFO - Starting comprehensive API tests...
2025-07-24 16:45:57,201 - INFO - Setting up test data...
2025-07-24 16:45:57,220 - INFO - [FAIL] POST /auth/register - FAIL: Register test user 1
2025-07-24 16:45:57,395 - INFO - [PASS] POST /auth/login - PASS: Login existing user 1
2025-07-24 16:45:57,396 - INFO - Logged in existing user: <EMAIL>
2025-07-24 16:45:57,410 - INFO - [FAIL] POST /auth/register - FAIL: Register test user 2
2025-07-24 16:45:57,585 - INFO - [PASS] POST /auth/login - PASS: Login existing user 2
2025-07-24 16:45:57,586 - INFO - Logged in existing user: <EMAIL>
2025-07-24 16:45:57,603 - INFO - [FAIL] POST /auth/register - FAIL: Register test user 3
2025-07-24 16:45:57,774 - INFO - [PASS] POST /auth/login - PASS: Login existing user 3
2025-07-24 16:45:57,774 - INFO - Logged in existing user: <EMAIL>
2025-07-24 16:45:57,775 - INFO - Setup complete. Tokens obtained: ['user_1', 'user_2', 'user_3']
2025-07-24 16:45:57,775 - INFO - Testing Authentication Endpoints...
2025-07-24 16:45:57,788 - INFO - [PASS] POST /auth/register - PASS: Register with invalid data
2025-07-24 16:45:57,804 - INFO - [PASS] POST /auth/login - PASS: Login with invalid credentials
2025-07-24 16:45:57,817 - INFO - [PASS] GET /auth/validate - PASS: Validate valid token
2025-07-24 16:45:57,825 - INFO - [PASS] GET /auth/validate - PASS: Validate invalid token
2025-07-24 16:45:57,864 - INFO - [PASS] POST /auth/refresh-token - PASS: Refresh valid token
2025-07-24 16:45:58,046 - INFO - [PASS] POST /auth/login - PASS: Login user 1 for backup token
2025-07-24 16:45:58,055 - INFO - [FAIL] POST /auth/logout - FAIL: Logout with valid token
2025-07-24 16:45:58,055 - INFO - Testing Subscription Endpoints...
2025-07-24 16:45:58,069 - INFO - [PASS] GET /plans - PASS: Get subscription plans
2025-07-24 16:45:58,088 - INFO - [PASS] GET /subscriptions - PASS: Get user subscriptions
2025-07-24 16:45:58,104 - INFO - [PASS] GET /subscriptions/current - PASS: Get current subscription
2025-07-24 16:45:58,152 - INFO - [PASS] POST /subscriptions - PASS: Create subscription
2025-07-24 16:45:58,177 - INFO - [PASS] DELETE /subscriptions/9 - PASS: Cancel subscription
2025-07-24 16:45:58,178 - INFO - Testing Team Management Endpoints...
2025-07-24 16:45:58,204 - INFO - [PASS] GET /teams - PASS: Get user teams
2025-07-24 16:45:58,225 - INFO - [FAIL] POST /teams - FAIL: Create new team
2025-07-24 16:45:58,225 - INFO - Testing User Management Endpoints...
2025-07-24 16:45:58,240 - INFO - [PASS] GET /users/profile - PASS: Get user profile
2025-07-24 16:45:58,277 - INFO - [PASS] PUT /users/profile - PASS: Update user profile
2025-07-24 16:45:58,440 - INFO - [PASS] POST /users/validate-password - PASS: Validate user password
2025-07-24 16:45:58,602 - INFO - [FAIL] POST /users/validate-password - FAIL: Validate wrong password
2025-07-24 16:45:58,602 - INFO - Testing Friend Management Endpoints...
2025-07-24 16:45:58,630 - INFO - [PASS] POST /friends/request - PASS: Send friend request
2025-07-24 16:45:58,672 - INFO - [FAIL] GET /friends/requests/sent - FAIL: Get sent friend requests
2025-07-24 16:45:58,723 - INFO - [FAIL] GET /friends/requests/received - FAIL: Get received friend requests
2025-07-24 16:45:58,751 - INFO - [PASS] GET /friends/list - PASS: Get friends list
2025-07-24 16:45:58,771 - INFO - [PASS] GET /friends/count - PASS: Get friend count
2025-07-24 16:45:58,786 - INFO - [PASS] GET /users/profile - PASS: Get user 2 profile for friendship check
2025-07-24 16:45:58,806 - INFO - [PASS] GET /friends/check - PASS: Check friendship status
2025-07-24 16:45:58,828 - INFO - [PASS] GET /friends/list - PASS: Get friends list for remark test
2025-07-24 16:45:58,846 - INFO - [FAIL] POST /friends/request - FAIL: Send friend request to non-existent user
2025-07-24 16:45:58,846 - INFO - Generating test report...
2025-07-24 16:45:58,865 - INFO - All tests completed in 1.66 seconds
2025-07-24 17:00:09,774 - INFO - Starting comprehensive API tests...
2025-07-24 17:00:09,775 - INFO - Setting up test data...
2025-07-24 17:00:09,791 - INFO - [FAIL] POST /auth/register - FAIL: Register test user 1
2025-07-24 17:00:09,972 - INFO - [PASS] POST /auth/login - PASS: Login existing user 1
2025-07-24 17:00:09,973 - INFO - Logged in existing user: <EMAIL>
2025-07-24 17:00:09,988 - INFO - [FAIL] POST /auth/register - FAIL: Register test user 2
2025-07-24 17:00:10,160 - INFO - [PASS] POST /auth/login - PASS: Login existing user 2
2025-07-24 17:00:10,161 - INFO - Logged in existing user: <EMAIL>
2025-07-24 17:00:10,175 - INFO - [FAIL] POST /auth/register - FAIL: Register test user 3
2025-07-24 17:00:10,340 - INFO - [PASS] POST /auth/login - PASS: Login existing user 3
2025-07-24 17:00:10,341 - INFO - Logged in existing user: <EMAIL>
2025-07-24 17:00:10,341 - INFO - Setup complete. Tokens obtained: ['user_1', 'user_2', 'user_3']
2025-07-24 17:00:10,341 - INFO - Testing Authentication Endpoints...
2025-07-24 17:00:10,351 - INFO - [PASS] POST /auth/register - PASS: Register with invalid data
2025-07-24 17:00:10,365 - INFO - [PASS] POST /auth/login - PASS: Login with invalid credentials
2025-07-24 17:00:10,376 - INFO - [PASS] GET /auth/validate - PASS: Validate valid token
2025-07-24 17:00:10,385 - INFO - [PASS] GET /auth/validate - PASS: Validate invalid token
2025-07-24 17:00:10,409 - INFO - [PASS] POST /auth/refresh-token - PASS: Refresh valid token
2025-07-24 17:00:10,582 - INFO - [PASS] POST /auth/login - PASS: Login user 1 for backup token
2025-07-24 17:00:10,589 - INFO - [FAIL] POST /auth/logout - FAIL: Logout with valid token
2025-07-24 17:00:10,589 - INFO - Testing Subscription Endpoints...
2025-07-24 17:00:10,602 - INFO - [PASS] GET /plans - PASS: Get subscription plans
2025-07-24 17:00:10,624 - INFO - [PASS] GET /subscriptions - PASS: Get user subscriptions
2025-07-24 17:00:10,637 - INFO - [PASS] GET /subscriptions/current - PASS: Get current subscription
2025-07-24 17:00:10,677 - INFO - [PASS] POST /subscriptions - PASS: Create subscription
2025-07-24 17:00:10,697 - INFO - [PASS] DELETE /subscriptions/10 - PASS: Cancel subscription
2025-07-24 17:00:10,697 - INFO - Testing Team Management Endpoints...
2025-07-24 17:00:10,719 - INFO - [PASS] GET /teams - PASS: Get user teams
2025-07-24 17:00:10,740 - INFO - [FAIL] POST /teams - FAIL: Create new team
2025-07-24 17:00:10,741 - INFO - Testing User Management Endpoints...
2025-07-24 17:00:10,758 - INFO - [PASS] GET /users/profile - PASS: Get user profile
2025-07-24 17:00:10,782 - INFO - [PASS] PUT /users/profile - PASS: Update user profile
2025-07-24 17:00:10,972 - INFO - [PASS] POST /users/validate-password - PASS: Validate user password
2025-07-24 17:00:11,129 - INFO - [FAIL] POST /users/validate-password - FAIL: Validate wrong password
2025-07-24 17:00:11,129 - INFO - Testing Friend Management Endpoints...
2025-07-24 17:00:11,148 - INFO - [FAIL] POST /friends/request - FAIL: Send friend request
2025-07-24 17:00:11,188 - INFO - [FAIL] GET /friends/requests/sent - FAIL: Get sent friend requests
2025-07-24 17:00:11,232 - INFO - [FAIL] GET /friends/requests/received - FAIL: Get received friend requests
2025-07-24 17:00:11,249 - INFO - [PASS] GET /friends/list - PASS: Get friends list
2025-07-24 17:00:11,266 - INFO - [PASS] GET /friends/count - PASS: Get friend count
2025-07-24 17:00:11,278 - INFO - [PASS] GET /users/profile - PASS: Get user 2 profile for friendship check
2025-07-24 17:00:11,294 - INFO - [PASS] GET /friends/check - PASS: Check friendship status
2025-07-24 17:00:11,310 - INFO - [PASS] GET /friends/list - PASS: Get friends list for remark test
2025-07-24 17:00:11,325 - INFO - [FAIL] POST /friends/request - FAIL: Send friend request to non-existent user
2025-07-24 17:00:11,326 - INFO - Generating test report...
2025-07-24 17:00:11,345 - INFO - All tests completed in 1.57 seconds
2025-07-24 17:04:03,903 - INFO - Starting comprehensive API tests...
2025-07-24 17:04:03,904 - INFO - Setting up test data...
2025-07-24 17:04:03,922 - INFO - [FAIL] POST /auth/register - FAIL: Register test user 1
2025-07-24 17:04:04,110 - INFO - [PASS] POST /auth/login - PASS: Login existing user 1
2025-07-24 17:04:04,111 - INFO - Logged in existing user: <EMAIL>
2025-07-24 17:04:04,128 - INFO - [FAIL] POST /auth/register - FAIL: Register test user 2
2025-07-24 17:04:04,307 - INFO - [PASS] POST /auth/login - PASS: Login existing user 2
2025-07-24 17:04:04,308 - INFO - Logged in existing user: <EMAIL>
2025-07-24 17:04:04,323 - INFO - [FAIL] POST /auth/register - FAIL: Register test user 3
2025-07-24 17:04:04,503 - INFO - [PASS] POST /auth/login - PASS: Login existing user 3
2025-07-24 17:04:04,504 - INFO - Logged in existing user: <EMAIL>
2025-07-24 17:04:04,504 - INFO - Setup complete. Tokens obtained: ['user_1', 'user_2', 'user_3']
2025-07-24 17:04:04,505 - INFO - Testing Authentication Endpoints...
2025-07-24 17:04:04,514 - INFO - [PASS] POST /auth/register - PASS: Register with invalid data
2025-07-24 17:04:04,528 - INFO - [PASS] POST /auth/login - PASS: Login with invalid credentials
2025-07-24 17:04:04,542 - INFO - [PASS] GET /auth/validate - PASS: Validate valid token
2025-07-24 17:04:04,555 - INFO - [PASS] GET /auth/validate - PASS: Validate invalid token
2025-07-24 17:04:04,593 - INFO - [PASS] POST /auth/refresh-token - PASS: Refresh valid token
2025-07-24 17:04:04,782 - INFO - [PASS] POST /auth/login - PASS: Login user 1 for backup token
2025-07-24 17:04:04,791 - INFO - [FAIL] POST /auth/logout - FAIL: Logout with valid token
2025-07-24 17:04:04,791 - INFO - Testing Subscription Endpoints...
2025-07-24 17:04:04,804 - INFO - [PASS] GET /plans - PASS: Get subscription plans
2025-07-24 17:04:04,831 - INFO - [PASS] GET /subscriptions - PASS: Get user subscriptions
2025-07-24 17:04:04,845 - INFO - [PASS] GET /subscriptions/current - PASS: Get current subscription
2025-07-24 17:04:04,938 - INFO - [PASS] POST /subscriptions - PASS: Create subscription
2025-07-24 17:04:04,988 - INFO - [PASS] DELETE /subscriptions/11 - PASS: Cancel subscription
2025-07-24 17:04:04,989 - INFO - Testing Team Management Endpoints...
2025-07-24 17:04:05,018 - INFO - [PASS] GET /teams - PASS: Get user teams
2025-07-24 17:04:05,037 - INFO - [FAIL] POST /teams - FAIL: Create new team
2025-07-24 17:04:05,038 - INFO - Testing User Management Endpoints...
2025-07-24 17:04:05,057 - INFO - [PASS] GET /users/profile - PASS: Get user profile
2025-07-24 17:04:05,078 - INFO - [PASS] PUT /users/profile - PASS: Update user profile
2025-07-24 17:04:05,238 - INFO - [PASS] POST /users/validate-password - PASS: Validate user password
2025-07-24 17:04:05,397 - INFO - [FAIL] POST /users/validate-password - FAIL: Validate wrong password (should fail)
2025-07-24 17:04:05,397 - INFO - Testing Friend Management Endpoints...
2025-07-24 17:04:05,416 - INFO - [FAIL] POST /friends/request - FAIL: Send friend request
2025-07-24 17:04:05,439 - INFO - [PASS] POST /friends/request - PASS: Send friend request (already exists - expected)
2025-07-24 17:04:05,485 - INFO - [FAIL] GET /friends/requests/sent - FAIL: Get sent friend requests (known DB issue)
2025-07-24 17:04:05,528 - INFO - [FAIL] GET /friends/requests/received - FAIL: Get received friend requests (known DB issue)
2025-07-24 17:04:05,546 - INFO - [PASS] GET /friends/list - PASS: Get friends list
2025-07-24 17:04:05,564 - INFO - [PASS] GET /friends/count - PASS: Get friend count
2025-07-24 17:04:05,577 - INFO - [PASS] GET /users/profile - PASS: Get user 2 profile for friendship check
2025-07-24 17:04:05,594 - INFO - [PASS] GET /friends/check - PASS: Check friendship status
2025-07-24 17:04:05,611 - INFO - [PASS] GET /friends/list - PASS: Get friends list for remark test
2025-07-24 17:04:05,626 - INFO - [FAIL] POST /friends/request - FAIL: Send friend request to non-existent user
2025-07-24 17:04:05,627 - INFO - Generating test report...
2025-07-24 17:04:05,646 - INFO - All tests completed in 1.74 seconds
2025-07-24 17:23:41,369 - INFO - Starting comprehensive API tests...
2025-07-24 17:23:41,370 - INFO - Setting up test data...
2025-07-24 17:23:41,384 - INFO - [FAIL] POST /auth/register - FAIL: Register test user 1
2025-07-24 17:23:41,527 - INFO - [PASS] POST /auth/login - PASS: Login existing user 1
2025-07-24 17:23:41,528 - INFO - Logged in existing user: <EMAIL>
2025-07-24 17:23:41,539 - INFO - [FAIL] POST /auth/register - FAIL: Register test user 2
2025-07-24 17:23:41,690 - INFO - [PASS] POST /auth/login - PASS: Login existing user 2
2025-07-24 17:23:41,691 - INFO - Logged in existing user: <EMAIL>
2025-07-24 17:23:41,704 - INFO - [FAIL] POST /auth/register - FAIL: Register test user 3
2025-07-24 17:23:41,877 - INFO - [PASS] POST /auth/login - PASS: Login existing user 3
2025-07-24 17:23:41,877 - INFO - Logged in existing user: <EMAIL>
2025-07-24 17:23:41,878 - INFO - Setup complete. Tokens obtained: ['user_1', 'user_2', 'user_3']
2025-07-24 17:23:41,878 - INFO - Testing Authentication Endpoints...
2025-07-24 17:23:41,890 - INFO - [PASS] POST /auth/register - PASS: Register with invalid data
2025-07-24 17:23:41,908 - INFO - [PASS] POST /auth/login - PASS: Login with invalid credentials
2025-07-24 17:23:41,923 - INFO - [PASS] GET /auth/validate - PASS: Validate valid token
2025-07-24 17:23:41,931 - INFO - [PASS] GET /auth/validate - PASS: Validate invalid token
2025-07-24 17:23:41,958 - INFO - [PASS] POST /auth/refresh-token - PASS: Refresh valid token
2025-07-24 17:23:42,143 - INFO - [PASS] POST /auth/login - PASS: Login user 1 for backup token
2025-07-24 17:23:42,150 - INFO - [FAIL] POST /auth/logout - FAIL: Logout with valid token
2025-07-24 17:23:42,152 - INFO - Testing Subscription Endpoints...
2025-07-24 17:23:42,165 - INFO - [PASS] GET /plans - PASS: Get subscription plans
2025-07-24 17:23:42,207 - INFO - [PASS] GET /subscriptions - PASS: Get user subscriptions
2025-07-24 17:23:42,220 - INFO - [PASS] GET /subscriptions/current - PASS: Get current subscription
2025-07-24 17:23:42,261 - INFO - [PASS] POST /subscriptions - PASS: Create subscription
2025-07-24 17:23:42,281 - INFO - [PASS] DELETE /subscriptions/12 - PASS: Cancel subscription
2025-07-24 17:23:42,281 - INFO - Testing Team Management Endpoints...
2025-07-24 17:23:42,301 - INFO - [PASS] GET /teams - PASS: Get user teams
2025-07-24 17:23:42,320 - INFO - [FAIL] POST /teams - FAIL: Create new team
2025-07-24 17:23:42,321 - INFO - Testing User Management Endpoints...
2025-07-24 17:23:42,336 - INFO - [PASS] GET /users/profile - PASS: Get user profile
2025-07-24 17:23:42,357 - INFO - [PASS] PUT /users/profile - PASS: Update user profile
2025-07-24 17:23:42,517 - INFO - [PASS] POST /users/validate-password - PASS: Validate user password
2025-07-24 17:23:42,675 - INFO - [FAIL] POST /users/validate-password - FAIL: Validate wrong password (should fail)
2025-07-24 17:23:42,676 - INFO - Testing Friend Management Endpoints...
2025-07-24 17:23:42,694 - INFO - [FAIL] POST /friends/request - FAIL: Send friend request
2025-07-24 17:23:42,715 - INFO - [PASS] POST /friends/request - PASS: Send friend request (already exists - expected)
2025-07-24 17:23:42,761 - INFO - [FAIL] GET /friends/requests/sent - FAIL: Get sent friend requests (known DB issue)
2025-07-24 17:23:42,811 - INFO - [FAIL] GET /friends/requests/received - FAIL: Get received friend requests (known DB issue)
2025-07-24 17:23:42,831 - INFO - [PASS] GET /friends/list - PASS: Get friends list
2025-07-24 17:23:42,851 - INFO - [PASS] GET /friends/count - PASS: Get friend count
2025-07-24 17:23:42,864 - INFO - [PASS] GET /users/profile - PASS: Get user 2 profile for friendship check
2025-07-24 17:23:42,883 - INFO - [PASS] GET /friends/check - PASS: Check friendship status
2025-07-24 17:23:42,903 - INFO - [PASS] GET /friends/list - PASS: Get friends list for remark test
2025-07-24 17:23:42,932 - INFO - [FAIL] POST /friends/request - FAIL: Send friend request to non-existent user
2025-07-24 17:23:42,932 - INFO - Generating test report...
2025-07-24 17:23:42,951 - INFO - All tests completed in 1.58 seconds
2025-07-24 17:33:42,565 - INFO - Starting comprehensive API testing...
2025-07-24 17:33:42,566 - INFO - Setting up test environment...
2025-07-24 17:33:42,777 - ERROR - Failed to obtain required tokens
2025-07-24 17:33:42,778 - ERROR - Failed to setup test environment
