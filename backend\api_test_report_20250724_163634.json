{"summary": {"test_run_timestamp": "2025-07-24T16:36:34.050448", "total_tests": 38, "passed": 22, "failed": 16, "skipped": 0, "success_rate": "57.9%", "base_url": "http://localhost:8080/api/v1"}, "categories": {"Authentication": {"total": 14, "passed": 10, "failed": 4, "success_rate": "71.4%"}, "Team Management": {"total": 6, "passed": 6, "failed": 0, "success_rate": "100.0%"}, "User Management": {"total": 5, "passed": 1, "failed": 4, "success_rate": "20.0%"}, "Friend Management": {"total": 8, "passed": 0, "failed": 8, "success_rate": "0.0%"}, "Subscriptions": {"total": 5, "passed": 5, "failed": 0, "success_rate": "100.0%"}}, "detailed_results": [{"timestamp": "2025-07-24T16:36:32.597037", "endpoint": "/auth/register", "method": "POST", "status": "FAIL", "status_code": 400, "message": "Register test user 1", "error": "", "response_data": {"code": 400, "message": "邮箱已被注册", "data": null, "timestamp": "2025-07-24 16:36:32"}}, {"timestamp": "2025-07-24T16:36:32.780402", "endpoint": "/auth/login", "method": "POST", "status": "PASS", "status_code": 200, "message": "Login existing user 1", "error": "", "response_data": {"code": 200, "message": "登录成功", "data": {"token": "eyJhbGciOiJIUzM4NCJ9.*******************************************************************************************************************************************************************************************************************************.2ZwhkigoCVZ6o6mUcLVU1KI36n41ewgVtJaI5zk2xgLsSZY_DaNdOhb7oNCcGLbw", "expiresIn": 604799224, "user": {"id": 14, "email": "<EMAIL>", "name": "Test User 1"}, "teams": [], "team": null, "teamSelectionSuccess": null}, "timestamp": "2025-07-24 16:36:32"}}, {"timestamp": "2025-07-24T16:36:32.797218", "endpoint": "/auth/register", "method": "POST", "status": "FAIL", "status_code": 400, "message": "Register test user 2", "error": "", "response_data": {"code": 400, "message": "邮箱已被注册", "data": null, "timestamp": "2025-07-24 16:36:32"}}, {"timestamp": "2025-07-24T16:36:32.965896", "endpoint": "/auth/login", "method": "POST", "status": "PASS", "status_code": 200, "message": "Login existing user 2", "error": "", "response_data": {"code": 200, "message": "登录成功", "data": {"token": "eyJhbGciOiJIUzM4NCJ9.*******************************************************************************************************************************************************************************************************************************.OC1XXu-gR0GGxGZrBeK7OvM3eLJ_zsxzoXQ6FV5gU5Mm3M9R6DyMVKSA52pyi2PQ", "expiresIn": 604799039, "user": {"id": 15, "email": "<EMAIL>", "name": "Test User 2"}, "teams": [], "team": null, "teamSelectionSuccess": null}, "timestamp": "2025-07-24 16:36:32"}}, {"timestamp": "2025-07-24T16:36:32.981973", "endpoint": "/auth/register", "method": "POST", "status": "FAIL", "status_code": 400, "message": "Register test user 3", "error": "", "response_data": {"code": 400, "message": "邮箱已被注册", "data": null, "timestamp": "2025-07-24 16:36:32"}}, {"timestamp": "2025-07-24T16:36:33.150264", "endpoint": "/auth/login", "method": "POST", "status": "PASS", "status_code": 200, "message": "Login existing user 3", "error": "", "response_data": {"code": 200, "message": "登录成功", "data": {"token": "eyJhbGciOiJIUzM4NCJ9.*******************************************************************************************************************************************************************************************************************************.s35VoqVC4Gg0ndgvpAy1N2CpXNhLPdIe28Nxt9DbgtFr3Pxvu2FQWsiVOsLIAf86", "expiresIn": 604799854, "user": {"id": 16, "email": "<EMAIL>", "name": "Test User 3"}, "teams": [], "team": null, "teamSelectionSuccess": null}, "timestamp": "2025-07-24 16:36:33"}}, {"timestamp": "2025-07-24T16:36:33.162265", "endpoint": "/auth/register", "method": "POST", "status": "PASS", "status_code": 400, "message": "Register with invalid data", "error": "", "response_data": {"code": 400, "message": "参数验证失败: {password=密码长度至少8位, email=邮箱格式不正确}", "data": null, "timestamp": "2025-07-24 16:36:33"}}, {"timestamp": "2025-07-24T16:36:33.180398", "endpoint": "/auth/login", "method": "POST", "status": "PASS", "status_code": 400, "message": "Login with invalid credentials", "error": "", "response_data": {"code": 400, "message": "邮箱或密码错误", "data": null, "timestamp": "2025-07-24 16:36:33"}}, {"timestamp": "2025-07-24T16:36:33.195423", "endpoint": "/auth/validate", "method": "GET", "status": "PASS", "status_code": 200, "message": "Validate valid token", "error": "", "response_data": {"code": 200, "message": "Token验证结果", "data": true, "timestamp": "2025-07-24 16:36:33"}}, {"timestamp": "2025-07-24T16:36:33.206379", "endpoint": "/auth/validate", "method": "GET", "status": "PASS", "status_code": 401, "message": "Validate invalid token", "error": "", "response_data": {"code": 401, "message": "未认证，请先登录", "data": null, "timestamp": "2025-07-24 16:36:33"}}, {"timestamp": "2025-07-24T16:36:33.233195", "endpoint": "/auth/refresh-token", "method": "POST", "status": "PASS", "status_code": 200, "message": "Refresh valid token", "error": "", "response_data": {"code": 200, "message": "Token刷新成功", "data": {"token": "eyJhbGciOiJIUzM4NCJ9.*******************************************************************************************************************************************************************************************************************************.b5_40nxhRpX0hHOlYrKzv6KSVV0gwjOfdS02FQi5joDsqUHHw3NHY9HUg1Y506L_", "expiresIn": 604799770, "user": null, "teams": null, "team": null, "teamSelectionSuccess": null}, "timestamp": "2025-07-24 16:36:33"}}, {"timestamp": "2025-07-24T16:36:33.405567", "endpoint": "/auth/login", "method": "POST", "status": "PASS", "status_code": 200, "message": "Login user 1 for backup token", "error": "", "response_data": {"code": 200, "message": "登录成功", "data": {"token": "eyJhbGciOiJIUzM4NCJ9.*******************************************************************************************************************************************************************************************************************************.8Y4_t5vtv9jP__0WB7lpAYPJ35ew1KKUK7URiKGxPJR9RLOWc4138Yz99SeIWfMY", "expiresIn": 604799600, "user": {"id": 14, "email": "<EMAIL>", "name": "Test User 1"}, "teams": [], "team": null, "teamSelectionSuccess": null}, "timestamp": "2025-07-24 16:36:33"}}, {"timestamp": "2025-07-24T16:36:33.415134", "endpoint": "/auth/logout", "method": "POST", "status": "FAIL", "status_code": 401, "message": "Logout with valid token", "error": "", "response_data": {"code": 401, "message": "未认证，请先登录", "data": null, "timestamp": "2025-07-24 16:36:33"}}, {"timestamp": "2025-07-24T16:36:33.429593", "endpoint": "/plans", "method": "GET", "status": "PASS", "status_code": 200, "message": "Get subscription plans", "error": "", "response_data": {"code": 200, "message": "success", "data": [{"id": 1, "name": "免费版", "description": "适合个人用户，可创建1个团队", "maxSize": 1, "price": 0.0, "isActive": true, "createdAt": "2025-07-23 09:40:17", "updatedAt": "2025-07-24 14:57:08"}, {"id": 2, "name": "标准版", "description": "适合小团队，可创建5个团队", "maxSize": 5, "price": 99.0, "isActive": true, "createdAt": "2025-07-23 09:40:17", "updatedAt": "2025-07-24 14:57:08"}, {"id": 3, "name": "专业版", "description": "适合中型企业，可创建20个团队", "maxSize": 20, "price": 299.0, "isActive": true, "createdAt": "2025-07-23 09:40:17", "updatedAt": "2025-07-24 14:57:08"}, {"id": 4, "name": "企业版", "description": "适合大型企业，可创建无限团队", "maxSize": 999999, "price": 999.0, "isActive": true, "createdAt": "2025-07-23 09:40:17", "updatedAt": "2025-07-24 14:57:08"}], "timestamp": "2025-07-24 16:36:33"}}, {"timestamp": "2025-07-24T16:36:33.444716", "endpoint": "/subscriptions", "method": "GET", "status": "PASS", "status_code": 200, "message": "Get user subscriptions", "error": "", "response_data": {"code": 200, "message": "success", "data": [], "timestamp": "2025-07-24 16:36:33"}}, {"timestamp": "2025-07-24T16:36:33.471229", "endpoint": "/subscriptions/current", "method": "GET", "status": "PASS", "status_code": 200, "message": "Get current subscription", "error": "", "response_data": {"code": 200, "message": "success", "data": null, "timestamp": "2025-07-24 16:36:33"}}, {"timestamp": "2025-07-24T16:36:33.541213", "endpoint": "/subscriptions", "method": "POST", "status": "PASS", "status_code": 200, "message": "Create subscription", "error": "", "response_data": {"code": 200, "message": "订阅创建成功", "data": {"id": 8, "accountId": 14, "subscriptionPlanId": 1, "planName": "免费版", "planDescription": "适合个人用户，可创建1个团队", "maxSize": 1, "price": 0.0, "startDate": "2025-07-24", "endDate": null, "status": "ACTIVE", "createdAt": "2025-07-24 16:36:33", "updatedAt": "2025-07-24 16:36:33"}, "timestamp": "2025-07-24 16:36:33"}}, {"timestamp": "2025-07-24T16:36:33.579466", "endpoint": "/subscriptions/8", "method": "DELETE", "status": "PASS", "status_code": 200, "message": "Cancel subscription", "error": "", "response_data": {"code": 200, "message": "订阅取消成功", "data": null, "timestamp": "2025-07-24 16:36:33"}}, {"timestamp": "2025-07-24T16:36:33.597467", "endpoint": "/teams", "method": "GET", "status": "PASS", "status_code": 200, "message": "Get user teams", "error": "", "response_data": {"code": 200, "message": "success", "data": [], "timestamp": "2025-07-24 16:36:33"}}, {"timestamp": "2025-07-24T16:36:33.663082", "endpoint": "/teams", "method": "POST", "status": "PASS", "status_code": 200, "message": "Create new team", "error": "", "response_data": {"code": 200, "message": "团队创建成功", "data": {"id": 13, "name": "Test Team API", "description": "Team created by API test", "createdBy": 14, "memberCount": 1, "isCreator": null, "lastAccessTime": null, "createdAt": "2025-07-24 16:36:33", "updatedAt": "2025-07-24 16:36:33"}, "timestamp": "2025-07-24 16:36:33"}}, {"timestamp": "2025-07-24T16:36:33.710873", "endpoint": "/auth/select-team", "method": "POST", "status": "PASS", "status_code": 200, "message": "Select created team", "error": "", "response_data": {"code": 200, "message": "团队选择成功", "data": {"token": "eyJhbGciOiJIUzM4NCJ9.**********************************************************************************************************************************************************************************************************************************************************************.JcrwNoJvN74Wf6C6GB-_Zgkth1lyJuB-gcQz6ZWD9MgXwxg2F1ZWAd2u_7OjK3Mq", "expiresIn": 604799294, "user": null, "teams": null, "team": {"id": 13, "name": null, "isCreator": true, "memberCount": null, "lastAccessTime": null}, "teamSelectionSuccess": true}, "timestamp": "2025-07-24 16:36:33"}}, {"timestamp": "2025-07-24T16:36:33.735786", "endpoint": "/teams/current", "method": "GET", "status": "PASS", "status_code": 200, "message": "Get current team details", "error": "", "response_data": {"code": 200, "message": "success", "data": {"id": 13, "name": "Test Team API", "description": "Team created by API test", "createdBy": 14, "memberCount": 1, "isCreator": null, "lastAccessTime": null, "createdAt": "2025-07-24 16:36:33", "updatedAt": "2025-07-24 16:36:33"}, "timestamp": "2025-07-24 16:36:33"}}, {"timestamp": "2025-07-24T16:36:33.795027", "endpoint": "/teams/current", "method": "PUT", "status": "PASS", "status_code": 200, "message": "Update team information", "error": "", "response_data": {"code": 200, "message": "团队信息更新成功", "data": {"id": 13, "name": "Updated Test Team", "description": "Updated description", "createdBy": 14, "memberCount": 1, "isCreator": null, "lastAccessTime": null, "createdAt": "2025-07-24 16:36:33", "updatedAt": "2025-07-24 16:36:33"}, "timestamp": "2025-07-24 16:36:33"}}, {"timestamp": "2025-07-24T16:36:33.835707", "endpoint": "/teams/current/members", "method": "GET", "status": "PASS", "status_code": 200, "message": "Get team members", "error": "", "response_data": {"code": 200, "message": "success", "data": {"list": [{"id": 12, "accountId": 14, "email": "<EMAIL>", "name": "Test User 1", "isCreator": true, "assignedAt": "2025-07-24 16:36:33", "lastAccessTime": "2025-07-24 16:36:33", "isActive": true}], "total": 1, "current": 1, "pageSize": 10}, "timestamp": "2025-07-24 16:36:33"}}, {"timestamp": "2025-07-24T16:36:33.875750", "endpoint": "/teams/current/members/invite", "method": "POST", "status": "PASS", "status_code": 200, "message": "Invite team members", "error": "", "response_data": {"code": 200, "message": "成员邀请成功", "data": null, "timestamp": "2025-07-24 16:36:33"}}, {"timestamp": "2025-07-24T16:36:33.885600", "endpoint": "/users/profile", "method": "GET", "status": "FAIL", "status_code": 401, "message": "Get user profile", "error": "", "response_data": {"code": 401, "message": "未认证，请先登录", "data": null, "timestamp": "2025-07-24 16:36:33"}}, {"timestamp": "2025-07-24T16:36:33.894175", "endpoint": "/users/profile", "method": "PUT", "status": "FAIL", "status_code": 401, "message": "Update user profile", "error": "", "response_data": {"code": 401, "message": "未认证，请先登录", "data": null, "timestamp": "2025-07-24 16:36:33"}}, {"timestamp": "2025-07-24T16:36:33.901704", "endpoint": "/users/validate-password", "method": "POST", "status": "FAIL", "status_code": 401, "message": "Validate user password", "error": "", "response_data": {"code": 401, "message": "未认证，请先登录", "data": null, "timestamp": "2025-07-24 16:36:33"}}, {"timestamp": "2025-07-24T16:36:33.914259", "endpoint": "/users/validate-password", "method": "POST", "status": "FAIL", "status_code": 401, "message": "Validate wrong password", "error": "", "response_data": {"code": 401, "message": "未认证，请先登录", "data": null, "timestamp": "2025-07-24 16:36:33"}}, {"timestamp": "2025-07-24T16:36:33.924731", "endpoint": "/friends/request", "method": "POST", "status": "FAIL", "status_code": 401, "message": "Send friend request", "error": "", "response_data": {"code": 401, "message": "未认证，请先登录", "data": null, "timestamp": "2025-07-24 16:36:33"}}, {"timestamp": "2025-07-24T16:36:33.932307", "endpoint": "/friends/requests/sent", "method": "GET", "status": "FAIL", "status_code": 401, "message": "Get sent friend requests", "error": "", "response_data": {"code": 401, "message": "未认证，请先登录", "data": null, "timestamp": "2025-07-24 16:36:33"}}, {"timestamp": "2025-07-24T16:36:33.982082", "endpoint": "/friends/requests/received", "method": "GET", "status": "FAIL", "status_code": 500, "message": "Get received friend requests", "error": "", "response_data": {"code": 500, "message": "系统内部错误，请联系管理员", "data": null, "timestamp": "2025-07-24 16:36:33"}}, {"timestamp": "2025-07-24T16:36:33.991937", "endpoint": "/friends/list", "method": "GET", "status": "FAIL", "status_code": 401, "message": "Get friends list", "error": "", "response_data": {"code": 401, "message": "未认证，请先登录", "data": null, "timestamp": "2025-07-24 16:36:33"}}, {"timestamp": "2025-07-24T16:36:34.000085", "endpoint": "/friends/count", "method": "GET", "status": "FAIL", "status_code": 401, "message": "Get friend count", "error": "", "response_data": {"code": 401, "message": "未认证，请先登录", "data": null, "timestamp": "2025-07-24 16:36:33"}}, {"timestamp": "2025-07-24T16:36:34.016084", "endpoint": "/users/profile", "method": "GET", "status": "PASS", "status_code": 200, "message": "Get user 2 profile for friendship check", "error": "", "response_data": {"code": 200, "message": "success", "data": {"id": 15, "email": "<EMAIL>", "name": "Test User 2", "defaultSubscriptionPlanId": 1, "createdAt": "2025-07-24 16:32:04", "updatedAt": "2025-07-24 16:32:04"}, "timestamp": "2025-07-24 16:36:34"}}, {"timestamp": "2025-07-24T16:36:34.030175", "endpoint": "/friends/check", "method": "GET", "status": "FAIL", "status_code": 401, "message": "Check friendship status", "error": "", "response_data": {"code": 401, "message": "未认证，请先登录", "data": null, "timestamp": "2025-07-24 16:36:34"}}, {"timestamp": "2025-07-24T16:36:34.040874", "endpoint": "/friends/list", "method": "GET", "status": "FAIL", "status_code": 401, "message": "Get friends list for remark test", "error": "", "response_data": {"code": 401, "message": "未认证，请先登录", "data": null, "timestamp": "2025-07-24 16:36:34"}}, {"timestamp": "2025-07-24T16:36:34.049451", "endpoint": "/friends/request", "method": "POST", "status": "FAIL", "status_code": 401, "message": "Send friend request to non-existent user", "error": "", "response_data": {"code": 401, "message": "未认证，请先登录", "data": null, "timestamp": "2025-07-24 16:36:34"}}]}